---
import type { SortField } from '@/types/Sorting'
import { QtyInput } from './react/QtyInput'
import { priceFormat } from '@/lib/priceFormat'
import { PhotoTooltip } from './react/PhotoTooltip'
import { CircleHelpIcon } from 'lucide-react'

//!!-------------------------- !EXPERIMENT COMPONENT! --------------------

interface Props {
  data: unknown
  initialSorting?: SortField[]
  cartMode?: boolean
  isMobile?: boolean
  viewMode?: 'table' | 'grid'
  cardImageSize?: number
  showCounter?: number | undefined
  enableMultiSort?: boolean
}

const {
  data,
  initialSorting = [],
  cartMode = false,
  isMobile = false,
  viewMode = 'table',
  cardImageSize = 450,
  showCounter = false,
  enableMultiSort = false
} = Astro.props
---

<table class='d-table d-table-pin-rows d-table-pin-cols d-table-xs d-table-zebra'>
  <thead>
    <tr class='sticky top-0'>
      {
        data.category?.columns?.map((column) => (
          <th class='bg-default-100/90 px-1 text-xs font-semibold backdrop-blur-sm sm:text-sm lg:px-2'>{column.title}</th>
        ))
      }
      <th class='bg-default-100/90 px-1 text-xs font-semibold backdrop-blur-sm sm:text-sm lg:px-2'>Фото</th>
      <th class='bg-default-100/90 px-1 text-xs font-semibold backdrop-blur-sm sm:text-sm lg:px-2'>Наличие</th>
      <th class='bg-default-100/90 px-1 text-xs font-semibold backdrop-blur-sm sm:text-sm lg:px-2'>Цена</th>
      <th class='bg-default-100/90 px-1 text-xs font-semibold backdrop-blur-sm sm:text-sm lg:px-2'>
        <div class='d-tooltip' data-tip='Оптовые цены от 10 000 руб.'>
          <div class='flex gap-1'>Опт <CircleHelpIcon className='w-5' /></div>
        </div>
      </th>
      <th class='bg-default-100/90 px-1 text-xs font-semibold backdrop-blur-sm sm:text-sm lg:px-2'>{cartMode ? 'Кол-во' : 'В корзину'}</th>
    </tr>
  </thead>

  <tbody>
    {
      (data.products?.data || data.data).map((item, index) => (
        <tr>
          {data.category?.columns?.map((column) => (
            <td class='px-1 text-xs lg:px-2 xl:text-sm'>
              <a href={`/catalog/products/${item.prod_id}`} class='hover:underline'>
                {item[column.keyname]}
              </a>
            </td>
          ))}

          <td>
            {/* <img src={`https://mirsalnikov.ru/data/rti/${item.prod_img}.jpg`} alt={item.prod_sku} class='h-12 w-12 cursor-pointer' /> */}
            <PhotoTooltip client:visible product={item} />
          </td>
          <td class='text-right xl:text-sm'>{item.prod_count}</td>
          <td class='text-right xl:text-sm'>{priceFormat(item.prod_price)}</td>
          <td class='text-right xl:text-sm'>{priceFormat(item.whosaleprice)}</td>
          <th>
            <QtyInput size='sm' mini client:visible product={item} cartMode={cartMode} initialValue={item.qty || 0} />
          </th>
        </tr>
      ))
    }
  </tbody>
</table>
