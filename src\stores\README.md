# Стор администратора

## Описание

Стор `admin.ts` предоставляет глобальное состояние для статуса администратора, совместимое с SSR в Astro.

## Использование

### В серверных функциях (Astro)

```typescript
import { getIsAdmin } from '@/stores/admin'

const isAdmin = getIsAdmin()
```

### В React компонентах

```typescript
import { useAdmin } from '@/hooks/useAdmin'

function MyComponent() {
  const { isAdmin, isUser } = useAdmin()
  
  return (
    <div>
      {isAdmin ? 'Админ панель' : 'Обычный пользователь'}
    </div>
  )
}
```

### Прямое использование стора

```typescript
import { isAdmin, setIsAdmin, getIsAdmin } from '@/stores/admin'

// Получить значение
const currentStatus = getIsAdmin()

// Установить значение
setIsAdmin(true)

// Подписаться на изменения
isAdmin.subscribe((newValue) => {
  console.log('Admin status changed:', newValue)
})
```

## Архитектура

- **nanostores**: Легковесная библиотека для управления состоянием
- **SSR совместимость**: Значение устанавливается в middleware
- **TypeScript**: Полная типизация
- **React интеграция**: Хук для удобного использования в компонентах

## Установка значения

Значение `isAdmin` автоматически устанавливается в `middleware.ts` на основе:
1. Переменной окружения `DEV` (по умолчанию)
2. Проверки токена `ctoken` через API `/api/cpan/auth/check`
