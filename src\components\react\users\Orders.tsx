import { useState } from 'react'
import {
  Table,
  TableHeader,
  TableBody,
  TableColumn,
  TableRow,
  TableCell,
  Modal,
  ModalContent,
  ModalHeader,
  ModalBody,
  Button,
  Input,
  Chip,
  Card,
  CardBody,
  CardHeader,
  Card<PERSON>ooter,
  <PERSON><PERSON>r,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>Footer
} from '@heroui/react'
import { priceFormat } from '@/lib/priceFormat'
import { Paginator } from '../Paginator'
import { getProductLink } from '@/lib/getproductlink'

interface OrderItem {
  prod_id: number
  prod_title: string
  prod_price: number
  qty: number
  prod_sku?: string
  prod_analogsku?: string
  prod_manuf?: string
  prod_purpose?: string
  prod_size?: string
  images?: Array<{ path: string; name: string; type: string }>
  orderCount?: number
}

interface OrderClient {
  client_name: string
  client_mail: string
  client_phone: string
  client_city: string
  client_country: string
  client_street: string
  client_house: string
  client_flat: string
  client_postindex: string
  client_cdekid?: string
  client_number?: number
  client_id?: number
}

interface Order {
  order_id: number
  order_datetime: string
  order_status: string
  order_price: number
  order_shippingprice: number
  order_shipping: string
  order_payment: string
  order_desc: string
  order_tracknumber?: string
  order_weight?: number
  order_gtd?: number
  order_coupons?: {
    discountVal: number
    personal: number
  }
  items?: OrderItem[]
  client?: OrderClient
  defsum?: number
}

export const UserOrders = ({ data, isMobile }) => {
  // console.log('🚀 ~ UserOrders ~ data:', data)
  const [orders, setOrders] = useState(data)
  const [searchValue, setSearchValue] = useState('')
  const [selectedOrder, setSelectedOrder] = useState<Order | null>(null)
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [activeTab, setActiveTab] = useState('items')

  const columns = [
    { key: 'prod_title', label: 'Наименование' },
    { key: 'qty', label: isMobile ? 'Кол-во' : 'Количество' },
    { key: 'prod_price', label: 'Цена' },
    { key: 'total', label: 'Сумма' }
  ]

  const handleOrderClick = async (order: Order) => {
    setSelectedOrder(order)
    setIsModalOpen(true)
    setActiveTab('items') // Сбрасываем активную вкладку при открытии нового заказа
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'В ожидании оплаты':
        return 'warning'
      case 'Оплачен':
        return 'primary'
      case 'Отменен':
        return 'danger'
      case 'Не обработан':
        return 'default'
      case 'Отправлен':
        return 'secondary'
      case 'Завершен':
        return 'default'
      default:
        return 'default'
    }
  }

  // Функция для форматирования даты
  const formatDisplayDate = (dateString) => {
    try {
      const date = new Date(dateString)
      return new Intl.DateTimeFormat('ru-RU', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      }).format(date)
    } catch (e) {
      return dateString
    }
  }

  // Функция для форматирования способа оплаты
  const formatPaymentMethod = (method) => {
    switch (method) {
      case 'card':
        return 'Банковской картой'
      case 'cash':
        return 'Наличными'
      default:
        return method
    }
  }

  // Функция для получения полного адреса
  const getFullAddress = (client) => {
    if (!client) return ''

    const parts = [
      client.client_country,
      client.client_postindex,
      client.client_city,
      client.client_street,
      client.client_house,
      client.client_flat ? `кв. ${client.client_flat}` : ''
    ].filter(Boolean)

    return parts.join(', ')
  }

  return (
    <div className='w-full'>
      <div className='mb-4 flex flex-wrap items-center justify-between rounded-lg bg-default-100 p-4'>
        <div>
          <h1 className='text-xl font-bold'>Заказы</h1>
          <div className='text-sm'>Всего: {orders?.meta?.totalCount}</div>
        </div>

        {/* <div className='flex items-center gap-2'>
          <Input placeholder='Поиск по заказам' value={searchValue} onChange={(e) => setSearchValue(e.target.value)} className='max-w-xs' />
        </div> */}
      </div>

      <div className='grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3'>
        {orders?.data?.map((order) => (
          <Card key={order.order_id} isPressable onPress={() => handleOrderClick(order)} className='rounded-lg border shadow-none dark:border-default-200'>
            <CardHeader className='flex items-center justify-between bg-default-50 px-4 py-3'>
              <div className='flex items-center gap-2'>
                <span className='text-lg font-bold'>Заказ #{order.order_id}</span>
                <Chip color={getStatusColor(order.order_status)} size='sm' variant='flat'>
                  {order.order_status}
                </Chip>
              </div>
              <div className='text-sm text-default-600'>{formatDisplayDate(order.order_datetime)}</div>
            </CardHeader>
            <Divider />
            <CardBody className='space-y-2 px-4 py-3'>
              {order.client && (
                <div className='mb-3'>
                  <div className='mb-1 text-default-600'>Получатель:</div>
                  <div className='font-medium'>{order.client.client_name}</div>
                  <div className='text-sm'>{order.client.client_phone}</div>
                  <div className='mt-1 text-sm text-default-600'>{getFullAddress(order.client)}</div>
                </div>
              )}

              <div className='grid grid-cols-2 gap-x-4 gap-y-2'>
                <div className='text-default-600'>Товары:</div>
                <div className='text-right font-medium'>{priceFormat(order.order_price)}</div>

                <div className='text-default-600'>Доставка:</div>
                <div className='text-right font-medium'>{priceFormat(order.order_shippingprice)}</div>

                <div className='text-default-600'>Способ доставки:</div>
                <div className='text-right font-medium'>{order.order_shipping}</div>

                <div className='text-default-600'>Способ оплаты:</div>
                <div className='text-right font-medium'>{formatPaymentMethod(order.order_payment)}</div>

                {order.order_coupons && order.order_coupons.discountVal > 0 && (
                  <>
                    <div className='text-default-600'>Скидка:</div>
                    <div className='text-right font-medium text-success'>{priceFormat(order.order_coupons.discountVal)}</div>
                  </>
                )}

                <div className='col-span-2 mt-1 flex justify-between border-t pt-2 text-lg font-semibold'>
                  <span>Итого:</span>
                  <span>{priceFormat(order.order_price + order.order_shippingprice)}</span>
                </div>
              </div>

              {order.order_desc && (
                <div className='mt-2 border-t pt-2'>
                  <span className='text-default-600'>Примечание:</span>
                  <p className='mt-1 text-sm'>{order.order_desc}</p>
                </div>
              )}

              {order.items && (
                <div className='mt-2 border-t pt-2'>
                  <span className='text-default-600'>Товаров в заказе:</span>
                  <span className='ml-2 font-medium'>{order.items.length}</span>
                </div>
              )}
            </CardBody>

            {order.order_tracknumber && (
              <CardFooter className='bg-default-50 px-4 py-3'>
                <div className='w-full'>
                  <span className='text-default-600'>Трек-номер:</span>
                  <a
                    href={`https://www.pochta.ru/tracking#${order.order_tracknumber}`}
                    target='_blank'
                    rel='noopener noreferrer'
                    className='ml-2 font-medium text-primary hover:underline'
                  >
                    {order.order_tracknumber}
                  </a>
                </div>
              </CardFooter>
            )}
          </Card>
        ))}
      </div>
      <div className='mt-5 flex justify-end'>
        <Paginator isMobile={isMobile} total={orders?.meta?.pageCount} initPage={orders?.meta?.currentPage} />
      </div>

      <Modal
        scrollBehavior='inside'
        placement={isMobile ? 'bottom' : 'auto'}
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        size={isMobile ? 'full' : '3xl'}
      >
        <ModalContent>
          <ModalHeader className='flex flex-col gap-1'>
            <div className='flex items-center gap-2'>
              <span>Заказ #{selectedOrder?.order_id}</span>
              {selectedOrder && (
                <Chip color={getStatusColor(selectedOrder.order_status)} size='sm'>
                  {selectedOrder.order_status}
                </Chip>
              )}
            </div>
            <div className='text-sm text-default-600'>{selectedOrder && formatDisplayDate(selectedOrder.order_datetime)}</div>
          </ModalHeader>
          <Divider />
          <ModalBody className='px-1'>
            <Tabs size={isMobile ? 'sm' : 'md'} selectedKey={activeTab} onSelectionChange={setActiveTab}>
              <Tab key='items' title='Товары'>
                <div className='mb-4'>
                  <h3 className='mb-2 text-lg font-bold'>Состав заказа</h3>
                  <Table
                    classNames={{
                      wrapper: 'p-0',
                      table: 'text-xs md:tex-base',
                      td: 'text-xs md:tex-base',
                      tr: 'text-xs md:tex-base',
                      th: 'text-xs md:tex-base'
                    }}
                    fullWidth
                    isCompact={!!isMobile}
                    isHeaderSticky
                    isStriped
                  >
                    <TableHeader>
                      {columns.map((column) => (
                        <TableColumn key={column.key}>{column.label}</TableColumn>
                      ))}
                    </TableHeader>
                    <TableBody>
                      {selectedOrder?.items?.map((item) => (
                        <TableRow key={item.prod_id}>
                          <TableCell>
                            <div className='flex items-center gap-2'>
                              <div>
                                <a href={getProductLink({product: item })} target='_blank' className='text-xs text-default-800 md:text-sm xl:text-base'>
                                  {item.prod_sku} {item.prod_analogsku} {item.prod_size}
                                </a>
                                {item.prod_purpose && <div className='text-xs text-default-600'>{item.prod_purpose}</div>}
                              </div>
                            </div>
                          </TableCell>
                          <TableCell>{item.item_count || item.orderCount || item.qty}</TableCell>
                          <TableCell>{priceFormat(selectedOrder.whosalePrices ? item.whosaleprice : item.prod_price)}</TableCell>
                          <TableCell>
                            {priceFormat(
                              (selectedOrder.whosalePrices ? item.whosaleprice : item.prod_price) * (item.item_count || item.orderCount || item.qty)
                            )}
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>

                  <div className='mt-4 flex flex-col items-end justify-end gap-4 text-sm md:text-base'>
                    <div className='flex justify-end'>
                      <div className='text-right'>
                        <div className='text-default-600'>Сумма: </div>
                        <div className='font-bold lg:text-lg'>{selectedOrder && priceFormat(selectedOrder.order_price)}</div>
                      </div>
                    </div>
                    <div>
                      {selectedOrder?.order_coupons && selectedOrder.order_coupons.discountVal > 0 && (
                        <div className='flex items-center justify-between md:mb-2'>
                          <div className='text-default-600'>Скидка: </div>
                          <div className='font-medium text-success'>{priceFormat(selectedOrder.order_coupons.discountVal)}</div>
                        </div>
                      )}
                      <div className='flex flex-col items-center justify-between'>
                        <div className='text-default-600'>Доставка: </div>
                        <div className='font-medium'>{priceFormat(selectedOrder?.order_shippingprice || 0)}</div>
                      </div>
                    </div>
                    <div className='flex justify-end'>
                      <div className='text-right'>
                        <div className='text-default-600'>Итого: </div>
                        <div className='font-bold lg:text-lg'>
                          {selectedOrder && priceFormat(selectedOrder.order_price + selectedOrder.order_shippingprice)}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </Tab>

              <Tab key='delivery' title='Доставка и оплата'>
                <div className='mb-1 grid grid-cols-1 gap-4 md:grid-cols-2 lg:mb-4'>
                  <div className='space-y-3'>
                    <div>
                      <div className='mb-1 text-default-600'>Способ доставки:</div>
                      <div className='font-medium'>{selectedOrder?.order_shipping}</div>
                    </div>

                    <div>
                      <div className='mb-1 text-default-600'>Стоимость доставки:</div>
                      <div className='font-medium'>{priceFormat(selectedOrder?.order_shippingprice || 0)}</div>
                    </div>

                    {/* {selectedOrder?.order_weight && selectedOrder.order_weight > 0 && (
                      <div>
                        <div className='mb-1 text-default-600'>Вес заказа:</div>
                        <div className='font-medium'>{selectedOrder.order_weight} г</div>
                      </div>
                    )} */}
                    {selectedOrder?.order_tracknumber && (
                      <div>
                        <div className='mb-1 text-default-600'>Трек-номер:</div>
                        <div className='font-medium'>
                          <a
                            href={`https://www.pochta.ru/tracking#${selectedOrder.order_tracknumber}`}
                            target='_blank'
                            rel='noopener noreferrer'
                            className='text-primary hover:underline'
                          >
                            {selectedOrder.order_tracknumber}
                          </a>
                        </div>
                      </div>
                    )}
                  </div>

                  <div className='space-y-3'>
                    <div>
                      <div className='mb-1 text-default-600'>Способ оплаты:</div>
                      <div className='font-medium'>{selectedOrder && formatPaymentMethod(selectedOrder.order_payment)}</div>
                    </div>

                    <div>
                      <div className='mb-1 text-default-600'>Сумма заказа:</div>
                      <div className='font-medium'>{priceFormat(selectedOrder?.order_price || 0)}</div>
                    </div>

                    {selectedOrder?.order_coupons && selectedOrder.order_coupons.discountVal > 0 && (
                      <div>
                        <div className='mb-1 text-default-600'>Скидка по купону:</div>
                        <div className='font-medium text-success'>{priceFormat(selectedOrder.order_coupons.discountVal)}</div>
                      </div>
                    )}

                    <div>
                      <div className='mb-1 text-default-600'>Итого к оплате:</div>
                      <div className='text-lg font-bold'>{selectedOrder && priceFormat(selectedOrder.order_price + selectedOrder.order_shippingprice)}</div>
                    </div>
                  </div>
                </div>
              </Tab>

              <Tab key='client' title='Получатель'>
                {selectedOrder?.client && (
                  <div className='grid grid-cols-1 gap-4 md:grid-cols-2'>
                    <div className='space-y-3'>
                      <div>
                        <div className='mb-1 text-default-600'>ФИО:</div>
                        <div className='font-medium'>{selectedOrder.client.client_name}</div>
                      </div>

                      <div>
                        <div className='mb-1 text-default-600'>Телефон:</div>
                        <div className='font-medium'>{selectedOrder.client.client_phone}</div>
                      </div>

                      <div>
                        <div className='mb-1 text-default-600'>Email:</div>
                        <div className='font-medium'>{selectedOrder.client.client_mail}</div>
                      </div>

                      {selectedOrder.client.client_number && (
                        <div>
                          <div className='mb-1 text-default-600'>Номер клиента:</div>
                          <div className='font-medium'>#{selectedOrder.client.client_number}</div>
                        </div>
                      )}
                    </div>

                    <div className='space-y-3'>
                      <div>
                        <div className='mb-1 text-default-600'>Адрес доставки:</div>
                        <div className='font-medium'>
                          {selectedOrder.client.client_country}, {selectedOrder.client.client_postindex}
                        </div>
                        <div className='font-medium'>
                          {selectedOrder.client.client_city}, {selectedOrder.client.client_street}, {selectedOrder.client.client_house}
                          {selectedOrder.client.client_flat && `, кв. ${selectedOrder.client.client_flat}`}
                        </div>
                      </div>

                      {selectedOrder.order_desc && (
                        <div>
                          <div className='mb-1 text-default-600'>Примечание к заказу:</div>
                          <div className='font-medium'>{selectedOrder.order_desc}</div>
                        </div>
                      )}
                    </div>
                  </div>
                )}

                {!selectedOrder?.client && <div className='py-8 text-center text-default-600'>Информация о получателе отсутствует</div>}
              </Tab>
            </Tabs>

            {!isMobile && <Divider className='my-4' />}
          </ModalBody>
          <ModalFooter>
            <div className='flex justify-end gap-2'>
              <Button color='primary' variant='light' onPress={() => setIsModalOpen(false)}>
                Закрыть
              </Button>
            </div>
          </ModalFooter>
        </ModalContent>
      </Modal>

      {orders?.data?.length === 0 && (
        <div className='rounded-lg bg-default-50 py-12 text-center'>
          <div className='text-xl font-medium text-default-600'>У вас пока нет заказов</div>
          <div className='mt-2 text-default-500'>Здесь будет отображаться история ваших заказов</div>
        </div>
      )}
    </div>
  )
}
