import { Card, CardBody, CardHeader, Divider, Link, <PERSON>over, <PERSON>overContent, PopoverTrigger, Tooltip } from '@heroui/react'
import { CircleHelpIcon, ExternalLinkIcon, KeyIcon } from 'lucide-react'
import { openProductEditor } from '@/stores/productEditor'
// import { navigate } from 'astro:transitions/client'
import { getKeyValue } from '@heroui/react'
import { SafeHTML } from './SafeHTML'
import { priceFormat } from '@/lib/priceFormat'
import { lazy, Suspense, memo, useMemo } from 'react'
import { useInView } from 'react-intersection-observer'
import {  useState, useEffect } from 'react'
import { CartItemSum } from './cart/CartItemSum'
import { getRtiImageUrl } from '@/lib/config'
// import { QuickViewButton } from './QuickViewButton'
import { whosalePrices } from '@/stores/cart'
import { useStore } from '@tanstack/react-store'

// Функция дебаунса для оптимизации обработчиков событий
function debounce(fn, delay) {
  let timer
  const debouncedFn = function (...args) {
    clearTimeout(timer)
    timer = setTimeout(() => fn.apply(this, args), delay)
  }
  debouncedFn.cancel = () => clearTimeout(timer)
  return debouncedFn
}

// Ленивая загрузка компонентов
const ImageViewer = lazy(() => import('./ImageViewer').then((module) => ({ default: module.ImageViewer })))
// Используем статический импорт QtyInput, так как он уже статически импортируется в ProductQuickView
import { QtyInput } from './QtyInput'
import { getProductLink } from '@/lib/utils'

// Мемоизированный компонент для изображения карточки с оптимизированной загрузкой
const CardImage = memo(({ item, index, isMobile }: { item: any; index: number; isMobile: boolean }) => {
  const imageUrl = getRtiImageUrl(`${item.prod_img || item.prod_analogsku}.jpg`)
  const imageHeight = isMobile ? 180 : 250

  if (index === 0) {
    // Первый товар — не ленивый, сразу img
    return (
      // <div className='relative z-0 flex justify-center'>
      //   <img
      //     src={imageUrl}
      //     alt={`${item.prod_purpose} ${item.prod_sku} - изображение 1`}
      //     height={imageHeight}
      //     style={{ height: imageHeight }}
      //     loading="eager"
      //     fetchPriority="high"
      //     className="relative z-10 shadow-black/5 opacity-100"
      //   />
      // </div>
      <ImageViewer imageHeight={imageHeight} product={item} isMobile={isMobile} />
    )
  }

  const { ref, inView } = useInView({
    triggerOnce: true,
    threshold: 0.1,
    rootMargin: isMobile ? '200px' : '100px'
  })

  return (
    <div ref={ref} className='relative z-0 flex justify-center'>
      {!inView ? (
        <div className='rounded-lg bg-default-100' style={{ height: imageHeight, width: '100%' }} />
      ) : (
        <Suspense fallback={<div className='rounded-lg bg-default-100' style={{ height: imageHeight, width: '100%' }} />}>
          <ImageViewer imageHeight={imageHeight} product={item} isMobile={isMobile} />
        </Suspense>
      )}
    </div>
  )
})

// Мемоизированный компонент для кнопки количества без анимации
const LazyQtyInput = memo(({ product, ...props }) => {
  const { ref, inView } = useInView({
    triggerOnce: true,
    threshold: 0.1,
    rootMargin: '50px'
  })

  return (
    <div ref={ref} style={{ minHeight: 36, minWidth: 112 }}>
      {!inView ? <div className='h-8 rounded-lg bg-warning-100' /> : <QtyInput {...props} product={product} />}
    </div>
  )
})

// Добавляем функцию проверки наличия (перед ProductCard)
const isInsufficientStock = (product) => {
  return product.prod_count < product.qty
}

// Мемоизированный компонент HighlightText для подсветки совпадений по searchvalue
const HighlightText = memo(({ text, searchvalue, isHTML = false }: { text: string; searchvalue: string; isHTML?: boolean }) => {
  // HTML-контент с тултипом
  if (isHTML) {
    return (
      <Tooltip
        content={<SafeHTML html={text} />}
        placement='bottom'
        showArrow
        classNames={{
          content: 'max-w-md max-h-[300px] overflow-auto'
        }}
      >
        <SafeHTML html={text} className='line-clamp-2' />
      </Tooltip>
    )
  }

  // Мемоизация разбиения текста
  const highlightedContent = useMemo(() => {
    if (!searchvalue) return text

    const lower = text?.toLowerCase() || ''
    const search = searchvalue.toLowerCase()

    if (!search || !lower.includes(search)) return text

    const parts = text.split(new RegExp(`(${search})`, 'gi'))

    return (
      <>
        {parts.map((part, i) =>
          part.toLowerCase() === search ? (
            <span key={i} className='rounded bg-warning-100 px-1'>
              {part}
            </span>
          ) : (
            <span key={i}>{part}</span>
          )
        )}
      </>
    )
  }, [text, searchvalue])

  return <div className='line-clamp-3'>{highlightedContent}</div>
})

// Компонент для отображения цены
const PriceDisplay = memo(({ product, isWholesale }) => {
  if (!isWholesale) {
    return <span className='font-semibold text-default-900'>{priceFormat(product.prod_price)}</span>
  }

  return (
    <div className='flex flex-wrap gap-1'>
      <span className='text-default-600 line-through'>{priceFormat(product.prod_price)}</span>
      <span className='text-default-900'>{priceFormat(product.whosaleprice)}</span>
    </div>
  )
})

// Интерфейс для пропсов ProductCard
interface ProductCardProps {
  item: any
  index: number
  columns: any[]
  mobileCustomColumns: any
  addToCartHandler: (product: any) => void
  cartMode: boolean
  isMobile: boolean
  $whosalePrices: boolean
  startIndex: number
  searchvalue?: string
  editorModeEnable?: boolean
  className?: string
  productUrlPrefix?: string
}

// Мемоизированная карточка продукта с оптимизированной функцией сравнения
const ProductCard = memo(
  ({
    item,
    index,
    columns,
    mobileCustomColumns,
    addToCartHandler,
    cartMode,
    isMobile,
    $whosalePrices,
    startIndex,
    searchvalue,
    editorModeEnable = false,
    className = '',
    productUrlPrefix
  }: ProductCardProps) => {
    // console.log(`[ProductsCards] ProductCard render for product ${item.prod_id}, qty=${item.qty}`);
    return (
      <Card
        className={`flex h-full w-full flex-col border dark:border-default-200 md:max-w-md ${cartMode && isInsufficientStock(item) ? 'bg-red-50 dark:bg-red-900/20' : ''} ${className}`}
        as='div'
        shadow='none'
        isFooterBlurred
      >
        <CardHeader className='flex items-center justify-center gap-1 text-ellipsis bg-default-100 p-1 text-sm md:text-base'>
          {editorModeEnable && (
            <button
              type='button'
              className='relative z-20 rounded-full bg-transparent p-2 hover:bg-default-200'
              onClick={(e) => {
                e.stopPropagation()
                e.preventDefault()
                openProductEditor(item.prod_id.toString())
              }}
              aria-label='Редактировать товар'
              title='Редактировать товар'
            >
              <KeyIcon className='h-5 w-5 text-default-600' />
            </button>
          )}
          {cartMode && (
            <div className='text-ellipsis rounded-lg border-2 p-1 px-2 text-sm font-semibold underline hover:underline dark:border-default-200 sm:no-underline'>
              {startIndex + index}
            </div>
          )}
          <a
            target='_blank'
            className='text-ellipsis text-center text-sm font-medium hover:underline lg:text-base'
            href={getProductLink({product: item, productUrlPrefix })}
            rel='noreferrer'
            aria-label={`Подробнее о товаре: ${item.prod_purpose} ${item.prod_sku}`}
          >
            <HighlightText text={`${item.prod_purpose} ${item.prod_sku} ${item.prod_manuf} ${item.prod_size}`} searchvalue={searchvalue || ''} />
          </a>
        </CardHeader>
        <CardBody className='flex flex-grow flex-col p-0'>
          <CardImage item={item} index={index} isMobile={isMobile} />
          <div className='flex flex-grow flex-col p-4'>
            <div className='mb-4 flex flex-grow flex-col gap-2 text-sm'>
              {columns.map((column) => (
                <div key={column.keyname} className='flex rounded-md p-1 px-2 odd:bg-default-100'>
                  <div className='w-1/2 shrink-0 font-semibold text-default-900'>{column.titleRender ? column.titleRender(column.title) : column.title}</div>
                  <div className='w-1/2 text-default-900'>
                    {mobileCustomColumns[column.keyname]?.render?.(item) ?? (
                      <div className={column.keyname === 'prod_note' ? 'whitespace-normal' : 'line-clamp-3'}>
                        <HighlightText text={getKeyValue(item, column.keyname) || ''} searchvalue={searchvalue || ''} isHTML={column.keyname === 'prod_note'} />
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
            <Divider />

            <div className='mt-2 flex items-end justify-between'>
              <div>
                {/* Показываем CartItemSum в режиме корзины */}
                {cartMode ? (
                  <div>
                    <div className='flex items-center gap-2 text-base'>
                      <span className='text-default-900'>Цена:</span>

                      <PriceDisplay product={item} isWholesale={$whosalePrices} />
                    </div>
                    <div className='flex items-center gap-2 text-base'>
                      <span className='text-default-900'>Сумма:</span>
                      <span className='font-semibold text-default-900'>
                        <CartItemSum product={item} wholesalePrice={false} />
                      </span>
                    </div>
                  </div>
                ) : (
                  <>
                    <div className='flex items-center gap-2 text-base'>
                      <span className='text-default-900'>Цена:</span>
                      <span className='font-semibold text-default-900'>{priceFormat(item.prod_price)}</span>
                    </div>
                    <div className='text-base'>
                      <Popover placement='bottom' showArrow={true}>
                        <PopoverTrigger>
                          <div className='flex items-center gap-2'>
                            <span className='text-default-900'>Опт:</span>
                            <span className='font-semibold text-default-800'>{priceFormat(item.whosaleprice)}</span>
                            <CircleHelpIcon className='ml-1 w-4 rounded-full shadow' aria-hidden='true' />
                          </div>
                        </PopoverTrigger>
                        <PopoverContent>
                          <div className='px-1 py-2'>
                            <div className='text-small font-bold'>Оптовые цены от 10 000 руб.</div>
                          </div>
                        </PopoverContent>
                      </Popover>
                    </div>
                  </>
                )}
              </div>

              <div className='flex flex-col items-stretch gap-2 justify-self-stretch'>
                <LazyQtyInput
                  onAdded={() => addToCartHandler(item)}
                  label='В корзину'
                  showRemoveBtn={cartMode}
                  cartMode={cartMode}
                  size='sm'
                  initialValue={cartMode && isInsufficientStock(item) ? item.prod_count : item.qty || 0}
                  product={item}
                />

                <div className='flex flex-col gap-2'>
                  <Link
                    size='sm'
                    className='flex justify-center gap-2 rounded-lg bg-default-200 p-1 text-center text-sm'
                    target='_blank'
                    color='foreground'
                    href={getProductLink({product: item, productUrlPrefix })}
                    aria-label={`Подробнее о товаре: ${item.prod_purpose || item.prod_sku}`}
                  >
                    <span className='text-xs'>Подробнее</span>
                    <ExternalLinkIcon className='w-5' aria-hidden='true' />
                  </Link>
                  {/* <QuickViewButton product={item} size='sm' /> */}
                </div>
              </div>
            </div>
          </div>
        </CardBody>
      </Card>
    )
  },
  // Кастомная функция сравнения для предотвращения ненужных перерендеров
  (prevProps, nextProps) => {
    // Сравниваем только важные пропсы
    return (
      prevProps.item.prod_id === nextProps.item.prod_id &&
      prevProps.item.qty === nextProps.item.qty &&
      prevProps.cartMode === nextProps.cartMode &&
      prevProps.isMobile === nextProps.isMobile &&
      prevProps.$whosalePrices === nextProps.$whosalePrices &&
      prevProps.searchvalue === nextProps.searchvalue &&
      prevProps.editorModeEnable === nextProps.editorModeEnable
    )
  }
)

// Интерфейс для ProductsCards
interface ProductsCardsProps {
  data: any
  customColumns: any
  addToCartHandler: (product: any) => void
  cartMode?: boolean
  startIndex?: number
  searchvalue?: string
  editorModeEnable?: boolean
  productUrlPrefix?: string
}

// Основной компонент списка продуктов
export const ProductsCards = ({
  data,
  customColumns,
  addToCartHandler,
  cartMode = false,
  startIndex = 1,
  searchvalue,
  editorModeEnable = false,
  productUrlPrefix
}: ProductsCardsProps) => {
  // console.warn('@mount ProductsCards');

  const [isMobile, setIsMobile] = useState(false)
  const $whosalePrices = useStore(whosalePrices)

  // Мемоизация обработки колонок для мобильной версии
  const mobileCustomColumns = useMemo(() => {
    return Object.entries(customColumns).reduce((acc, [key, column]) => {
      if (column.isMobile !== false && column.enable !== false && column.keyname !== 'number') {
        acc[key] = column
      }
      return acc
    }, {})
  }, [customColumns])

  // Мемоизация данных и колонок
  const items = useMemo(() => data.products?.data || data.data, [data])
  const columns = useMemo(
    () => [...(data.category?.columns || data.columns), ...Object.values(mobileCustomColumns)],
    [data.category?.columns, data.columns, mobileCustomColumns]
  )

  // Оптимизированный useEffect с дебаунсом для проверки мобильного устройства
  useEffect(() => {
    const checkMobile = () => setIsMobile(window.innerWidth < 768)

    // Дебаунс функция для предотвращения частых вызовов
    const debouncedCheck = debounce(checkMobile, 100)

    checkMobile() // Начальная проверка
    window.addEventListener('resize', debouncedCheck)

    return () => {
      window.removeEventListener('resize', debouncedCheck)
      debouncedCheck.cancel() // Очистка таймера дебаунса
    }
  }, [])

  return (
    <div className='grid grid-cols-1 items-stretch gap-4 md:grid-cols-2 lg:grid-cols-3 lg:gap-6'>
      {items.map((item, index) => (
        <div key={item.prod_id} className={`h-full ${items.length === 1 ? 'justify-self-start' : 'justify-self-center'}`}>
          <ProductCard
            className='h-full'
            item={item}
            index={index}
            columns={columns}
            $whosalePrices={$whosalePrices}
            mobileCustomColumns={mobileCustomColumns}
            addToCartHandler={addToCartHandler}
            cartMode={cartMode}
            isMobile={isMobile}
            startIndex={startIndex}
            searchvalue={searchvalue}
            editorModeEnable={editorModeEnable}
            productUrlPrefix={productUrlPrefix}
          />
        </div>
      ))}
    </div>
  )
}

ProductsCards.displayName = 'ProductsCards'
