import { priceFormat } from '@/lib/priceFormat'
import { trnColumns } from '@/lib/trnColumns'
import { getRtiImageUrl } from '@/lib/config'
import type { CategoryData, CategoryProduct } from '@/types/GetCategoryProduct'
import { Card, CardFooter, Image, Link, Tooltip } from '@heroui/react'
import { ExternalLinkIcon } from 'lucide-react'
import { getProductLink } from '@/lib/getproductlink'

// (property) Props.columns ?: {
//   sort: number;
//   ID: number;
//   keyname: string;
//   title: string;
//   sorted: boolean;
//   slot: boolean;
//   cat_id: number;
// }[] | undefined

interface Props {
  product: CategoryProduct
  ind?: number
  cardImageSize?: number
  addToCartComponent?: React.ReactNode
  columns?: CategoryData['category']['columns']
}

// Обновляем функцию рендера подсвеченного текста
const HighlightText = ({ text }: { text: string }) => {
  const parts = text.split(/(__ais-highlight__|__\/ais-highlight__)/g)

  return (
    <>
      {parts.map((part, i) => {
        if (part === '__ais-highlight__' || part === '__/ais-highlight__') return null
        const isHighlighted = i > 0 && parts[i - 1] === '__ais-highlight__'
        const uniqueKey = `${part}-${i}-${isHighlighted}`

        return isHighlighted ? (
          <span key={uniqueKey} className='overflow-ellipsis rounded-sm bg-warning-200 px-1'>
            {part}
          </span>
        ) : (
          <span key={uniqueKey} className='overflow-ellipsis'>
            {part}
          </span>
        )
      })}
    </>
  )
}

// Безопасный рендер HTML
const SafeHTML = ({ html }: { html: string }) => {
  return (
    <div 
      className='overflow-ellipsis'
      ref={(el) => {
        if (el) el.innerHTML = html
      }}
    />
  )
}

// Универсальный компонент для рендеринга поля
const renderFieldItem = (key: string, displayValue: string | undefined, containerClass: string) => {
  const cleanValue = displayValue?.replace(/__ais-highlight__/g, '')?.replace(/__\/ais-highlight__/g, '') || ''
  const needsTooltip = cleanValue.length > 100

  // Добавляем проверку на серверный рендеринг
  const isServer = typeof window === 'undefined'

  const FieldContent = () => (
    <div className='flex flex-wrap gap-2 overflow-hidden md:max-w-[200px] lg:flex-col'>
      <div className='flex text-sm font-semibold'>{trnColumns(key)}</div>
      <div className='line-clamp-3 overflow-ellipsis whitespace-break-spaces text-wrap text-sm font-normal'>
        {displayValue ? (
          key === 'prod_note' ? (
            isServer ? (
              // SSR-рендеринг: чистый текст без HTML
              <div>{cleanValue.replace(/<[^>]*>?/gm, '')}</div>
            ) : (
              // Клиентский рендеринг: с HTML
              <SafeHTML html={cleanValue} />
            )
          ) : (
            <HighlightText text={displayValue} />
          )
        ) : null}
      </div>
    </div>
  )

  return (
    <div className={`${containerClass} flex items-center rounded-lg p-2 text-sm md:p-0`} key={key}>
      {needsTooltip ? (
        <Tooltip content={<SafeHTML html={cleanValue} />}>
          <div className='w-full'>
            <FieldContent />
          </div>
        </Tooltip>
      ) : (
        <FieldContent />
      )}
    </div>
  )
}

export const ProductSearchCard = ({ product, ind, cardImageSize = 80, addToCartComponent, columns = [] }: Props) => {
  // console.log("🚀 ~ ProductSearchCard ~ columns:", columns)
  // Получаем все ключи из columns для проверки дублирования
  const columnKeys = columns.map((col) => col.keyname)

  // Дефолтные поля, которые показываем если нет columns
  const defaultFields = [
    { key: 'prod_sku', label: 'Артикул' },
    { key: 'prod_analogsku', label: 'Аналог' }
  ]

  // Функция для рендеринга полей
  const renderField = (key: string, label: string) => {
    const formattedValue = product._formatted?.[key as keyof typeof product._formatted]
    const originalValue = product[key as keyof typeof product]
    const hasHighlight = formattedValue?.includes('__ais-highlight__')
    const displayValue = hasHighlight || columnKeys.includes(key) ? formattedValue : originalValue

    return renderFieldItem(key, displayValue, 'odd:bg-default-100 md:odd:bg-transparent text-default-700')
  }

  // Собираем все уникальные поля для отображения
  const allFormattedFields = Object.keys(product._formatted || {})
    .filter((key) => !columnKeys.includes(key)) // Исключаем поля из columns
    .filter((key) => product._formatted?.[key]?.includes?.('__ais-highlight__')) // Только с хайлайтом

  return (
    <div className='flex flex-col flex-wrap items-center justify-between gap-2 rounded-lg border p-1 py-2 transition-colors hover:bg-default-50 dark:border-default-100 dark:bg-transparent sm:flex-row sm:p-2 md:gap-4 xl:gap-10'>
      <div className='_border flex w-full flex-1 flex-col flex-wrap items-center justify-center gap-3 rounded-lg dark:border-default-300 sm:flex-row'>
        <div className='font-semibold text-default-700'>{ind}</div>
        <div className='flex-shrink-0'>
          <Card isFooterBlurred className='border-none' radius='lg'>
            <Image
              radius="sm"
              src={getRtiImageUrl(`${product.prod_img || product.prod_analogsku}.jpg`)}
              shadow='lg'
              className='z-0 w-60 border dark:brightness-75 sm:w-36'
              removeWrapper
              width={100}
              height={100}
              loading='lazy'
              alt={product.prod_sku}
            />
            <CardFooter className='absolute bottom-0 z-10 justify-center overflow-hidden rounded-lg border-1 border-white/20 p-0 py-1 shadow-small before:rounded-xl before:bg-white/10'>
              <div className='flex items-center justify-center gap-2'>
                <div className={`h-3 w-3 rounded-full ${product.prod_count > 0 ? 'bg-success-500' : 'bg-default-500'}`} />
                <span className='text-xs text-default-800'>
                  {product.prod_count > 0 ? 'В наличии' : 'Предзаказ'}
                  {product.prod_count > 0 && <span className='font-semibold'> {product.prod_count} шт</span>}
                </span>
              </div>
            </CardFooter>
          </Card>
        </div>
        <div className='flex min-w-0 flex-1 flex-col gap-1'>
          <div className='flex-wrap items-start justify-start justify-items-end sm:gap-3 lg:flex lg:flex-row lg:justify-between lg:gap-5'>
            {/* Основные поля из columns или дефолтные */}
            {columns.length > 0 ? columns.map((col) => renderField(col.keyname, col.title)) : defaultFields.map((field) => renderField(field.key, field.label))}

            {/* Дополнительные подсвеченные поля из _formatted */}
            {allFormattedFields.map((key) =>
              renderFieldItem(key, product._formatted?.[key], 'md:max-w-40 odd:bg-default-100 md:odd:bg-transparent text-default-900')
            )}
          </div>
        </div>
      </div>

      <div className='flex w-full flex-row items-stretch justify-end gap-1 sm:w-auto sm:flex-col sm:justify-stretch'>
        {addToCartComponent}
        <Link
          className='flex justify-center gap-2 rounded-lg bg-default-200 px-2 text-sm sm:py-2'
          target='_blank'
          color='foreground'
          size='sm'
          href={getProductLink({product })}
        >
          <span className='text-center text-xs'>Подробнее</span>
          <ExternalLinkIcon className='w-5' />
        </Link>
      </div>
    </div>
  )
}
