import { useState, memo, lazy, type MouseEvent, useCallback, useMemo } from 'react'
import { useInView } from 'react-intersection-observer'
import {
  Table,
  TableHeader,
  TableBody,
  TableColumn,
  TableRow,
  TableCell,
  getKeyValue,
  Tooltip,
  Modal,
  Button,
  ModalContent,
  ModalBody
} from '@heroui/react'
import { StatsTooltip } from './StatsTooltip'
import { ArrowDownNarrowWide, ArrowUpNarrowWide, ArrowUpDown, CircleHelpIcon, ShoppingBasketIcon, CameraIcon, KeyIcon, ShoppingCartIcon } from 'lucide-react'
import { openProductEditor } from '@/stores/productEditor'
import { updateURLParams } from '@/stores/qs'
import { SafeHTML } from './SafeHTML'

import { priceFormat } from '$lib/priceFormat'
import { getRtiImageUrl } from '@/lib/config'
import type { SortField } from '@/types/Sorting'
import type { CategoryProduct } from '@/types/GetCategoryProduct'
import { CartItemSum } from './cart/CartItemSum'
import { addToast } from '@heroui/toast'
import { ProductsCards } from './ProductsCards'
import { whosalePrices, cartItems } from '@/stores/cart'
import { useStore } from '@tanstack/react-store'

// Используем статический импорт QtyInput и PreorderButton, так как они уже статически импортируются в других компонентах
import { QtyInput } from './QtyInput'
import { PreorderButton } from './PreorderButton'
import { getProductLink } from '@/lib/utils'
const PhotoTooltip = lazy(() => import('./PhotoTooltip').then((module) => ({ default: module.PhotoTooltip })))

// Интерфейсы для ленивых компонентов
interface LazyQtyInputProps {
  product: any
  onAdded?: (product: any) => void
  showRemoveBtn?: boolean
  cartMode?: boolean
  size?: 'sm' | 'md' | 'lg'
  mini?: boolean
  initialValue?: number
}

interface LazyPreorderButtonProps {
  product: any
  label?: string
  mini?: boolean
  size?: 'sm' | 'md' | 'lg'
  isTabler?: boolean
}

const QtySkeleton = ({ cartMode = false }) => {
  if (cartMode) {
    return (
      <div className='flex h-8 w-32 animate-pulse rounded-lg'>
        <div className='h-8 w-12 animate-pulse rounded-l-lg bg-danger-100' />
        <div className='h-8 w-full animate-pulse rounded-r-lg bg-default-200' />
      </div>
    )
  }
  return (
    <div className='mx-auto flex h-8 w-16 justify-center rounded-lg bg-warning/20 py-1'>
      <ShoppingCartIcon className='text-center text-warning-700' />
    </div>
  )
}

const LazyQtyInput = memo(({ product, onAdded, showRemoveBtn, cartMode, size, mini, initialValue }: LazyQtyInputProps) => {
  // console.log(`[ProductList] LazyQtyInput render for product ${product.prod_id}, qty=${product.qty}, initialValue=${initialValue}`);

  const { ref, inView } = useInView({
    triggerOnce: true,
    threshold: 0.1,
    rootMargin: '50px'
  })

  return (
    <div className='w-24' ref={ref}>
      {inView ? (
        <QtyInput
          onAdded={onAdded}
          showRemoveBtn={showRemoveBtn}
          cartMode={cartMode}
          size={size}
          mini={mini}
          initialValue={initialValue}
          product={product}
        />
      ) : (
        <QtySkeleton cartMode={cartMode} />
      )}
    </div>
  )
})

// Мемоизированный компонент для кнопки предзаказа
const LazyPreorderButton = memo(({ product, mini = false, size = 'sm', isTabler = true }: LazyPreorderButtonProps) => {
  const { ref, inView } = useInView({
    triggerOnce: true,
    threshold: 0.1,
    rootMargin: '50px'
  })

  return (
    <div ref={ref}>
      {inView ? (
        <PreorderButton isTabler={isTabler} product={product} mini={mini} size={size} />
      ) : (
        <div className='h-6 w-6 animate-pulse rounded-lg bg-default-100' />
      )}
    </div>
  )
})

interface Props {
  data: { products?: { data: CategoryProduct[] }; data?: CategoryProduct[]; columns?: any; category?: { columns?: any } }
  initialSorting?: SortField[]
  cartMode?: boolean
  isMobile?: boolean
  viewMode?: 'table' | 'grid'
  cardImageSize?: number
  showCounter?: number | undefined
  enableMultiSort?: boolean
  startIndex?: number
  enableSorting?: boolean
  forceCartModeUpdate?: boolean
  editorModeEnable?: boolean
  searchvalue?: string
  productUrlPrefix?: string
}

export const ProductList = ({
  data,
  initialSorting = [],
  cartMode = false,
  viewMode = 'table',
  enableMultiSort = false,
  startIndex = 1,
  enableSorting = true,
  forceCartModeUpdate = false,
  editorModeEnable = false,
  productUrlPrefix,
  ...rest
}: Props) => {
  // Получаем значение whosalePrices из хранилища
  const $whosalePrices = useStore(whosalePrices)

  // Получаем значение cartItems из хранилища
  const $cartItems = useStore(cartItems)
  //console.log('🚀 ~ ProductList ~ viewMode:', viewMode)
  const [sortFields, setSortFields] = useState<SortField[]>(initialSorting || [])
  const [isImageModalOpen, setImageModalOpen] = useState(false)
  const [selectedImage, setSelectedImage] = useState('')


  // Формируем productsData: если cartMode, qty берём из cartItems
  const productsData: CategoryProduct[] = (data?.products?.data || data?.data || []).map((product) => {
    if (cartMode) {
      const cartItem = $cartItems[product.prod_id]
      return {
        ...product,
        qty: product.qty || cartItem?.qty || 0
      }
    }
    return product
  })

  const addToCartHandler = useCallback((product) => {
    addToast({
      title: (
        <div>
          {product.prod_purpose} {product.prod_sku} <div>добавлен в корзину</div>
        </div>
      ),
      description: (
        <a target='_blank' rel='noreferrer' href='/cart'>
          <Button variant='flat' size='sm' color='warning'>
            Перейти в корзину
          </Button>
        </a>
      ),
      icon: <ShoppingBasketIcon />,
      timeout: 2000,
      shouldShowTimeoutProgress: true
    })
  }, [])

  const handleColumnClick = async (columnKey: string) => {
    setSortFields((prev) => {
      let newSortFields: SortField[]

      if (!enableMultiSort) {
        // Если множественная сортировка отключена, работаем только с одним полем.
        const existingField = prev.find((field) => field.column === columnKey)
        if (existingField) {
          // Если поле уже есть — меняем направление или удаляем его.
          if (existingField.direction === 'asc') {
            newSortFields = [{ column: columnKey, direction: 'desc', order: 1 }]
          } else {
            newSortFields = [] // отменяем сортировку для колонки
          }
        } else {
          // Если колонки ещё нет, устанавливаем сортировку по ней (asc)
          newSortFields = [{ column: columnKey, direction: 'asc', order: 1 }]
        }
      } else {
        // Множественная сортировка включена — логика как в оригинальном варианте
        newSortFields = [...prev]
        const existingFieldIndex = newSortFields.findIndex((field) => field.column === columnKey)

        if (existingFieldIndex !== -1) {
          const currentDirection = newSortFields[existingFieldIndex].direction

          if (currentDirection === 'asc') {
            newSortFields[existingFieldIndex].direction = 'desc'
          } else {
            newSortFields.splice(existingFieldIndex, 1)
            // Переустанавливаем порядок для оставшихся полей
            newSortFields = newSortFields.map((field, index) => ({
              ...field,
              order: index + 1
            }))
          }
        } else {
          newSortFields.push({
            column: columnKey,
            direction: 'asc',
            order: newSortFields.length + 1
          })
        }
      }

      // Формируем строку запроса сортировки
      const sortQuery = newSortFields
        .sort((a, b) => a.order - b.order)
        .map((field) => `${field.column}:${field.direction}`)
        .join(',')

      //console.log('Sort query:', sortQuery)

      updateURLParams({
        sorting: JSON.stringify(newSortFields)
      })

      return newSortFields
    })
  }

  const renderSortIcon = (columnKey: string) => {
    const sortField = sortFields?.find?.((field) => field.column === columnKey)
    if (!sortField) return <ArrowUpDown className='h-4 w-4 text-gray-400' />

    return sortField.direction === 'asc' ? <ArrowUpNarrowWide className='h-4 w-4' /> : <ArrowDownNarrowWide className='h-4 w-4' />
  }

  // const RenderCol = memo(({ column }) => (
  //   <div className='flex items-center gap-2 cursor-pointer select-none' onClick={() => handleColumnClick(column.keyname)}>
  //     <span>{column.title}</span>
  //     <div className='flex items-center gap-1'>
  //       {renderSortIcon(column.keyname)}
  //       {sortFields.find((field) => field.column === column.keyname) && (
  //         <span className='text-xs font-bold text-primary'>{sortFields.find((field) => field.column === column.keyname)?.order}</span>
  //       )}
  //     </div>
  //   </div>
  // ))

  // const RenderCol = ({ column }) => (
  const RenderCol = memo(({ column }) => {
    // //console.log('rendercol: ', column)
    // Не кастомные! столбцы из БД

    return (
      <div
        className={`flex shrink grow items-center justify-center gap-1 lg:gap-2 ${enableSorting ? 'cursor-pointer select-none' : ''}`}
        onClick={enableSorting ? () => handleColumnClick(column.keyname) : undefined}
        onKeyDown={
          enableSorting
            ? (e) => {
                if (e.key === 'Enter' || e.key === ' ') {
                  handleColumnClick(column.keyname)
                }
              }
            : undefined
        }
        role={enableSorting ? 'button' : undefined}
        tabIndex={enableSorting ? 0 : undefined}
      >
        {/* {column.titleRender ? column.titleRender(column.title) : column.title} */}
        <span className='text-xs lg:text-sm'>{column.title}</span>

        {enableSorting && (
          <div className='flex items-center'>
            {renderSortIcon(column.keyname)}
            {sortFields?.find((field) => field.column === column.keyname) && (
              <span className='text-xs font-bold text-primary'>{sortFields?.find((field) => field.column === column.keyname)?.order}</span>
            )}
          </div>
        )}
      </div>
    )
  })

  const openImageHandler = useCallback((e: MouseEvent<HTMLSpanElement, MouseEvent>, product) => {
    e.preventDefault()
    setSelectedImage(getRtiImageUrl(`${product?.prod_img || product?.prod_analogsku}.jpg`))
    setImageModalOpen(true)
  }, [])

  interface LazyPhotoTooltipProps {
    product: any
  }

  const LazyPhotoTooltip = memo(({ product }: LazyPhotoTooltipProps) => {
    const { ref, inView } = useInView({
      triggerOnce: true,
      threshold: 0.1,
      rootMargin: '50px'
    })

    return (
      <div ref={ref}>
        {inView ? (
          <PhotoTooltip product={product} />
        ) : (
          <CameraIcon />
        )}
      </div>
    )
  })

  const customColumns = {
    number: {
      isMobile: true,
      custom: true,
      keyname: 'number',
      title: '№',
      align: 'center',
      width: 30,
      enable: cartMode,
      render: (product: any, rowIndex: number) => {
        return <span className='font-medium'>{startIndex + rowIndex}</span>
      }
    },
    edit: {
      isMobile: true,
      custom: true,
      keyname: 'edit',
      title: 'Ред.',
      align: 'center',
      width: 40,
      enable: editorModeEnable,
      render: (product: any) => {
        return (
          <button
            type='button'
            className='relative z-20 rounded-full bg-transparent p-2 hover:bg-default-200'
            onClick={(e) => {
              e.stopPropagation()
              e.preventDefault()
              openProductEditor(product.prod_id.toString())
            }}
            aria-label='Редактировать товар'
            title='Редактировать товар'
          >
            <KeyIcon className='h-5 w-5 text-default-600' />
          </button>
        )
      }
    },
    qty: {
      isMobile: true,
      custom: true,
      align: 'center',
      enable: true,
      keyname: 'qty',
      title: 'Наличие',
      // width: 110,

      render: (product) =>
        product.prod_count > 0 || cartMode ? (
          editorModeEnable ? (
            <div>
              <StatsTooltip product={product} editorModeEnable={editorModeEnable}>
                <span className='text-right'> {product.prod_count} </span>
              </StatsTooltip>
            </div>
          ) : (
            <span className='text-right'> {product.prod_count} </span>
          )
        ) : (
          editorModeEnable ? (
            <div>
              <StatsTooltip product={product} editorModeEnable={editorModeEnable}>
                <span className='w-8'>
                  <LazyPreorderButton product={product} size='sm' isTabler={true} />
                </span>
              </StatsTooltip>
            </div>
          ) : (
            <span className='w-8'>
              <LazyPreorderButton product={product} size='sm' isTabler={true} />
            </span>
          )
        )
      //<span className='text-right'> {product.prod_count} </span>
    },
    image: {
      isMobile: false,
      custom: true,
      enable: true,
      keyname: 'image',
      title: 'Фото',
      align: 'center',
      // width: 30,

      render: useCallback(
        (product) => {
          // Используем кнопку вместо div с role="button"
          return (
            <button
              type='button'
              className='bg-transparent'
              onClick={(e) => openImageHandler(e as unknown as MouseEvent<HTMLSpanElement, MouseEvent>, product)}
            >
              <LazyPhotoTooltip product={product} />
            </button>
          )
        },
        [openImageHandler, LazyPhotoTooltip]
      )
      // render: (product) => <input type='number' value={0}></input>
    },
    wholesalePrice: {
      isMobile: false,
      custom: true,
      enable: !cartMode,
      keyname: 'wholesalePrice',
      title: 'Опт',
      align: 'end',
      // width: 70,
      titleRender: (text: string) => (
        <Tooltip showArrow content='Оптовые цены от 10 000 руб.'>
          <div className='flex items-center justify-end text-right'>
            <span className='text-right'>{text}</span> <CircleHelpIcon className='ml-1 w-4' />
          </div>
        </Tooltip>
      ),
      render: (product) => <span> {priceFormat(product.whosaleprice)} </span>
      // render: (product) => <input type='number' value={0}></input>
    },
    price: {
      isMobile: false,
      custom: true,
      enable: true,
      keyname: 'price',
      title: 'Цена',
      align: 'end',
      // width: 70,
      render: (product) => {
        // Если cartMode включен и whosalePrices тоже включен
        if (cartMode && $whosalePrices) {
          return (
            <div className='flex flex-wrap gap-1'>
              <span className='text-default-500 line-through'>{priceFormat(product.prod_price)}</span> <span>{priceFormat(product.whosaleprice)}</span>
            </div>
          )
        }
        // Если cartMode включен, но whosalePrices выключен
        if (cartMode) {
          return <span>{priceFormat(product.prod_price)}</span>
        }
        // Если cartMode выключен (обычный режим)

        return <span>{priceFormat(product.prod_price)}</span>
      }
    },

    tocart: {
      isMobile: false,
      custom: true,
      enable: true,
      keyname: 'tocart',
      title: !cartMode ? 'В корзину' : 'Кол-во',
      align: 'center',
      width: cartMode ? 150 : 120,
      render: useCallback(
        (product) => {
          // console.log(`[ProductList] tocart.render for product ${product.prod_id}, qty=${product.qty}`);
          return (
            <LazyQtyInput
              key={`qty-${product.prod_id}`}
              onAdded={() => addToCartHandler(product)}
              showRemoveBtn={cartMode}
              cartMode={cartMode}
              size='sm'
              mini
              // initialValue={cartMode && isInsufficientStock(product) ? product.prod_count : product.qty || 0}
              initialValue={product.qty || 0}
              product={product}
            />
          )
        },
        [cartMode, addToCartHandler]
      )
      // render: (product) => <AddToCartButton />

      // render: (product) => <input type='number' value={0}></input>
    },
    sum: {
      isMobile: false,
      custom: true,
      enable: cartMode,
      keyname: 'sum',
      title: 'Сумма',
      render: (product) => <CartItemSum product={product} wholesalePrice={$whosalePrices} />
    }
  }

  // Добавляем функцию проверки наличия
  const isInsufficientStock = (product) => {
    return product.prod_count < product.qty
  }

  // HighlightText для подсветки совпадений по searchvalue
  const HighlightText = memo(({ text, searchvalue, isHTML = false }: { text: string; searchvalue: string; isHTML?: boolean }) => {
    // Обработка HTML-контента
    if (isHTML) {
      return (
        <Tooltip
          content={<SafeHTML html={text} />}
          placement="bottom"
          showArrow
          disableAnimation
          closeDelay={0}
          classNames={{
            base: 'z-50',
            content: 'max-w-md max-h-[300px] overflow-auto p-2 rounded-lg shadow-xl'
          }}
        >
          <SafeHTML html={text} className="line-clamp-3" />
        </Tooltip>
      )
    }

    // Мемоизация разбиения текста для улучшения производительности
    const highlightedContent = useMemo(() => {
      // Если нет поискового запроса или текста, просто возвращаем исходный текст
      if (!searchvalue || !text) return text;

      // Декодируем searchvalue из URL и очищаем
      let decodedSearchValue: string;
      try {
        decodedSearchValue = decodeURIComponent(searchvalue).trim();
      } catch (e) {
        // Если декодирование не удалось, используем исходное значение
        decodedSearchValue = searchvalue.trim();
      }

      // Проверяем минимальную длину поискового запроса (3 символа)
      if (decodedSearchValue.length < 3) return text;

      // Приводим текст к нижнему регистру для сравнения
      const lowerText = text.toLowerCase().trim();
      const lowerSearch = decodedSearchValue.toLowerCase();

      // Если поисковый запрос не содержится в тексте, возвращаем исходный текст
      if (!lowerText.includes(lowerSearch)) return text;

      // Экранируем спецсимволы регулярных выражений в поисковом запросе
      const escapedSearch = decodedSearchValue.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');

      // Разбиваем текст на части для подсветки, используя глобальный регистронезависимый поиск
      const parts = text.split(new RegExp(`(${escapedSearch})`, 'gi'));

      return (
        <>
          {parts.map((part, i) => {
            // Проверяем, является ли часть совпадением (регистронезависимо)
            const isHighlighted = part.toLowerCase() === lowerSearch;
            const uniqueKey = `${i}-${part.length}-${isHighlighted}`;

            return isHighlighted ? (
              <span key={uniqueKey} className='rounded bg-warning-100 px-1'>
                {part}
              </span>
            ) : (
              <span key={uniqueKey}>{part}</span>
            );
          })}
        </>
      );
    }, [text, searchvalue]);

    return highlightedContent;
  })

  const ProductsTable = () => {
    // console.warn('@mount ProductsTable');
    // console.log(`[ProductList] ProductsTable render, cartMode=${cartMode}`);

    let rowCounter = startIndex

    return (
      <Table
        radius='none'
        shadow='none'
        className='rounded-none border-none contain-content'
        classNames={{
          // base: 'overflow-x-auto rounded-none',
          table: 'relative min-h-auto rounded-none text-center table-fixed xl:!table-auto',
          thead: 'rounded-none border rounded-none sr-only text-xs lg:text-sm',
          th: 'rounded-none border border-default-200 text-xs lg:text-sm overflow-hidden text-ellipsis whitespace-nowrap font-semibold text-default-600 px-1 lg:px-2 max-w-[200px]',
          wrapper: 'rounded-none border-none',
          td: 'relative rounded-none border border-default-300 px-1 lg:px-2 text-xs lg:text-sm xl:text-base overflow-hidden text-ellipsis whitespace-nowrap max-w-[200px] !before:block text-center text-black dark:text-default-700 font-medium tex',
          tr: 'relative rounded-none border-none even:bg-default-100 dark:odd:bg-default-50 text-center [&.insufficient]:bg-red-50 dark:[&.insufficient]:bg-red-900/20 max-w-[200px]'
        }}
        isHeaderSticky
        // isStriped
        layout='auto'
        removeWrapper
        // isCompact
        fullWidth
        disabledBehavior='all'
        // disableAnimation
        aria-label='Товары'
      >
        <TableHeader
          columns={[
            ...(editorModeEnable ? [customColumns.edit] : []),
            ...(cartMode ? [customColumns.number] : []),
            ...(data.category?.columns || data.columns),
            ...Object.values(customColumns).filter((col) => col.enable !== false && col.keyname !== 'number' && col.keyname !== 'edit')
          ]}
        >
          {(column) => (
            <TableColumn width={column.width || 'auto'} align={column.align || 'start'} key={column.keyname}>
              {column.custom ? (
                <div className='overflow-hidden text-ellipsis whitespace-nowrap text-center text-xs lg:text-sm'>
                  {column.titleRender ? column.titleRender(column.title) : column.title}
                </div>
              ) : (
                <RenderCol column={column} />
              )}
            </TableColumn>
          )}
        </TableHeader>
        <TableBody items={productsData}>
          {(item) => {
            // Увеличиваем счетчик для каждой строки
            const currentIndex = rowCounter++

            // console.log(`[ProductList] TableRow render for product ${item.prod_id}, qty=${item.qty}, prod_count=${item.prod_count}`);
            return (
              <TableRow key={`row-${item.prod_id}`} className={cartMode && isInsufficientStock(item) ? 'insufficient' : ''}>
                {(columnKey) => (
                  <TableCell align='center' className={columnKey === 'edit' ? 'z-10' : ''}>
                    {columnKey === 'number' ? (
                      <span className='font-medium'>{currentIndex}</span>
                    ) : customColumns[columnKey]?.render ? (
                      (() => {
                        // console.log(`[ProductList] customColumns[${columnKey}].render for product ${item.prod_id}, qty=${item.qty}`);
                        return customColumns[columnKey].render(cartMode && isInsufficientStock(item) ? { ...item, qty: item.prod_count } : item)
                      })()
                    ) : (
                      <div className="cell-wrapper">
                        <a
                          className='relative z-10 overflow-hidden text-ellipsis whitespace-nowrap text-center text-xs before:pointer-events-none hover:underline lg:text-sm xl:text-base select-text'
                          data-astro-prefetch='tap'
                          target='_blank'
                          href={getProductLink({product: item, productUrlPrefix })}
                          title={columnKey !== 'prod_note' ? getKeyValue(item, columnKey) : ''}
                          rel='noreferrer'
                          draggable={false}
                        >
                          <HighlightText text={getKeyValue(item, columnKey) || ''} searchvalue={rest.searchvalue || ''} isHTML={columnKey === 'prod_note'} />
                        </a>
                      </div>
                    )}
                  </TableCell>
                )}
              </TableRow>
            )
          }}
        </TableBody>
      </Table>
    )
  }

  // Функции для работы со статистикой удалены, так как теперь используется компонент StatsTooltip
  // console.log(`[ProductList] render, viewMode=${viewMode}, cartMode=${cartMode}, productsData:`, productsData);

  return (
    <>
      {/* <div className='mb-3 flex justify-end'>
        <ViewSwitcher />
      </div> */}

      {viewMode === 'table' && <ProductsTable />}
      {viewMode === 'grid' && (
        <ProductsCards
          data={{
            ...data,
            products: { ...(data?.products || {}), data: productsData },
            data: productsData
          }}
          customColumns={customColumns}
          addToCartHandler={addToCartHandler}
          cartMode={cartMode}
          startIndex={startIndex}
          searchvalue={rest.searchvalue}
          editorModeEnable={editorModeEnable}
          productUrlPrefix={productUrlPrefix}
        />
      )}

      <Modal
        size='5xl'
        isOpen={isImageModalOpen}
        onOpenChange={setImageModalOpen}
        // className="bg-background/60 backdrop-opacity-40"
        closeButton={
          <Button isIconOnly color='danger' variant='flat'>
            <span className='text-xl font-bold text-danger'>x</span>
          </Button>
        }
      >
        <ModalContent>
          {(onClose) => (
            <ModalBody>
              <img src={selectedImage} alt='Изображение' className='h-auto max-h-screen w-full' />
            </ModalBody>
          )}
        </ModalContent>
      </Modal>
    </>
  )
}
