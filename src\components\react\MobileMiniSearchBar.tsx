import { Autocomplete, AutocompleteItem, Button } from '@heroui/react'
import { useAsyncList } from '@react-stately/data'
import { SearchIcon } from 'lucide-react'
import { useRef } from 'react'
import { clearStoreState, updateURLParams } from '@/stores/qs'
import { navigate } from 'astro:transitions/client'
import { getProductLink } from '@/lib/utils'

// Интерфейс для товара
interface Product {
  prod_id: number
  prod_sku: string
  prod_img?: string
  prod_count: number
  prod_purpose?: string
  prod_size?: string
  prod_manuf?: string
  _formatted?: {
    prod_id?: number
    prod_purpose?: string
    prod_sku?: string
    prod_size?: string
    prod_img?: string
    [key: string]: unknown
  }
  [key: string]: unknown
}

export const MobileMiniSearchBar = () => {
  const inputRef = useRef<HTMLInputElement>(null)

  // Используем useAsyncList для асинхронной фильтрации
  let list = useAsyncList<Product>({
    async load({ signal, filterText }) {
      if (!filterText || filterText.length < 2) return { items: [] }
      const baseUrl = import.meta.env.DEV ? import.meta.env.PUBLIC_API_URL_DEV : import.meta.env.PUBLIC_API_URL
      const searchUrl = `${baseUrl}/service/instantsearch/`
      const resp = await fetch(searchUrl, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ q: filterText }),
        signal
      })
      if (!resp.ok) return { items: [] }
      const data = await resp.json()
      return { items: data?.hits || [] }
    }
  })

  // Обработчик выбора товара
  const handleSelect = (key: React.Key | null) => {
    if (!key) return

    const item = list.getItem(key.toString()) as Product
    if (item) {
      // Сначала очищаем список и поле ввода
      list.setFilterText('')

      // Используем промис от navigate для перехода на страницу товара
      navigate(getProductLink({product: item }))
        .then(() => {
          // После завершения навигации сбрасываем список еще раз
          list.reload()
          list.setFilterText('')
        })
    }
  }

  // Обработчик полного поиска
  async function searchHandler() {
    if (!list.filterText || list.filterText.length < 1) return
    clearStoreState()
    const qsf = updateURLParams({ page: 1 }, '')
    await navigate(`/search/${encodeURIComponent(list.filterText)}?${qsf}`)
    list.setFilterText('')
  }

  return (
    <div className='relative mt-1 flex items-center rounded-lg bg-default-50 dark:bg-default-100'>
      <div className='flex-1' style={{ maxWidth: '300px' }}>
        <Autocomplete
          inputValue={list.filterText}
          onInputChange={list.setFilterText}
          isLoading={list.isLoading}
          items={list.items}
          placeholder='Поиск'
          aria-label='Поиск по каталогу'
          size='sm'
          ref={inputRef}
          onSelectionChange={handleSelect}
          allowsCustomValue
        >
          {(item: Product) => (
            <AutocompleteItem key={String(item.prod_id || item._formatted?.prod_id)} textValue={item.prod_purpose || item._formatted?.prod_purpose || ''}>
              <div className='flex flex-col'>
                <span className='truncate text-sm font-medium'>{item.prod_purpose || item._formatted?.prod_purpose} {item.prod_manuf}</span>
                <span className='text-xs text-default-800'>{item.prod_sku || item._formatted?.prod_sku} {item.prod_size || item._formatted?.prod_size}</span>
              </div>
            </AutocompleteItem>
          )}
        </Autocomplete>
      </div>
      <Button onPress={searchHandler} size='sm' isIconOnly className='ml-2' aria-label='Найти'>
        <SearchIcon size={18} />
      </Button>
    </div>
  )
}
