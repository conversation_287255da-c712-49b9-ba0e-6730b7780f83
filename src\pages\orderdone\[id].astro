---
import Layout from '@layouts/Layout.astro'
import { YooKassa } from '@/components/react/YooKassa'
import { Spinner } from '@heroui/react'

const { id } = Astro.params
const paymentlink = Astro.url.searchParams.get('paymentlink')
const confirmation_token = Astro.url.searchParams.get('confirmation_token')
const payment = Astro.url.searchParams.get('payment')
const amount = Astro.url.searchParams.get('amount')
---

<script is:inline define:vars={{ paymentlink }}>
  let s = 5
  if (paymentlink) {
    const timerInterval = setInterval(() => {
      if (s > 0) {
        s--
        window.document.getElementById('timerslot').innerHTML = s
      }
      if (s == 0) {
        window.location.replace(paymentlink)
        clearInterval(timerInterval)
      }
    }, 1000)
  }
</script>
<Layout title={`${id} Заказ успешно отправлен`}>
  <head>
    <!-- Запрет индексации страницы поисковыми системами -->
    <meta name='robots' content='noindex, nofollow, noarchive' />
    <meta name='googlebot' content='noindex, nofollow, noarchive' />
    <meta name='bingbot' content='noindex, nofollow, noarchive' />
    <meta name='yandex' content='noindex, nofollow, noarchive' />
    <meta name='slurp' content='noindex, nofollow, noarchive' />
  </head>
  <div>
    <div class='mt-20 rounded-md bg-white py-4'>
      <div class='mx-auto max-w-2xl px-6 text-center'>
        <h2 class='text-lg font-semibold text-default-800 md:text-2xl'>
          {'Номер вашего заказа'}
          <span class='rounded bg-default-800 px-1 text-default-100'>#{id}</span>
        </h2>
        <p class='mt-4 text-default-700'>
          {!confirmation_token && <p>Ваш заказ будет обработан в ближайшее время</p>}

          {
            paymentlink && (
              <div>
                Через <b id='timerslot'>5</b> секунд, вы будете перенаправлены на форму оплаты.
              </div>
            )
          }

          {!paymentlink && !confirmation_token && <div>Реквизиты для оплаты будут высланы на электронную почту после обработки заказа.</div>}
        </p>

        {/* Отображаем форму оплаты YooKassa, если выбран способ оплаты "card" и есть токен */}
        {
          payment === 'card' && confirmation_token && (
            <div>
              <YooKassa client:only='react' orderId={id} confirmation_token={confirmation_token}>
                <div slot='fallback'>
                  <div class='my-4 flex items-center justify-center gap-4'>
                    <Spinner color='warning' client:load /> Загрузка платежной формы...
                  </div>
                </div>
              </YooKassa>
            </div>
          )
        }
      </div>
    </div>
  </div>
</Layout>
