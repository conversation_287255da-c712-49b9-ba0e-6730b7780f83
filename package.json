{"name": "rti-astro-react", "type": "module", "version": "0.0.1", "scripts": {"dev": "astro dev", "build": "astro build", "preview": "astro preview", "astro": "astro", "check": "astro check", "test": "jest", "test:watch": "jest --watch"}, "dependencies": {"@astrojs/check": "^0.9.4", "@astrojs/node": "9.4.3", "@astrojs/react": "4.3.1", "@astrojs/tailwind": "^6.0.2", "@heroui/react": "^2.7.10", "@million/lint": "^1.0.14", "@nanostores/persistent": "^0.10.2", "@nanostores/react": "^0.8.4", "@overlapmedia/imagemapper": "^2.0.6", "@playform/compress": "^0.1.9", "@playwright/test": "^1.52.0", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-scroll-area": "^1.2.9", "@react-stately/data": "^3.13.0", "@stepperize/react": "^4.2.0", "@tailwindcss/vite": "^4.1.8", "@tanstack/react-query": "^5.79.2", "@tanstack/react-store": "^0.7.1", "@tanstack/react-table": "^8.21.3", "@tanstack/react-virtual": "^3.13.9", "@tanstack/store": "^0.7.1", "@trpc/client": "^11.2.0", "@trpc/react-query": "^11.2.0", "@trpc/tanstack-react-query": "^11.2.0", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "astro": "5.13.7", "astro-loading-indicator": "^0.7.0", "astro-purgecss": "^5.2.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "embla-carousel-react": "^8.6.0", "framer-motion": "^12.15.0", "lucide-react": "^0.474.0", "nanostores": "^0.11.4", "numeralize-ru": "^2.0.0", "query-string": "^9.2.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-hook-form": "^7.57.0", "react-intersection-observer": "^9.16.0", "tailwind-merge": "^2.6.0", "tailwindcss": "3.4.17", "tailwindcss-animate": "^1.0.7", "typescript": "^5.8.3", "ua-parser-js": "^2.0.3"}, "devDependencies": {"playwright": "^1.52.0", "@trpc/server": "^11.2.0", "@types/http-proxy-middleware": "^1.0.0", "bun": "^1.2.15", "daisyui": "^4.12.24", "http-proxy-middleware": "^3.0.5", "prettier-plugin-astro": "^0.14.1", "prettier-plugin-tailwindcss": "^0.6.12", "tailwind-scrollbar": "^3.1.0"}}