---
import { trpc } from '@/trpc'
import { Button, Image } from '@heroui/react'
import { ExternalLinkIcon, EyeIcon } from 'lucide-react'
import { getRtiImageUrl } from '@/lib/config'

interface Props {
  id: number
}

const { id  } = Astro.props

const { product } = await trpc.products.getProductById.query(id)

const productImageUrl = getRtiImageUrl(`${product?.prod_img || product?.prod_analogsku}.jpg`)

---

<div>
  <div class='flex items-center gap-2 text-sm'>
    <Image 
      isZoomed 
      client:idle 
      width={80} 
      src={productImageUrl}
      alt={product?.prod_purpose} 
    />

    <a class='flex gap-2 p-2 text-sm hover:underline' target='_blank' color='foreground' href={`/catalog/products/${product.prod_id}`}>
      <div class='text-default-800'>{product?.prod_purpose} - {product?.prod_sku} {product?.prod_type}</div>
    </a>
    <Button client:idle isIconOnly size='sm'><EyeIcon /></Button>
  </div>
</div>
