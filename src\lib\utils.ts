import type { CategoryProduct } from '@/types/GetCategoryProduct';
import { clsx, type ClassValue } from 'clsx'
import { twMerge } from 'tailwind-merge'

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export function formatInitials(fullName: string | undefined): string {
  if (!fullName) return '';
  
  return fullName
    .split(' ')
    .filter(Boolean) // Убираем пустые строки
    .map(word => word.charAt(0).toUpperCase())
    .join('')
    .slice(0, 2); // Берем только первые два инициала
}
export const getProductLink = ({ product, productUrlPrefix }: { product: CategoryProduct; productUrlPrefix?: string }) => {
  console.log("🚀 ~ getProductLink ~ productUrlPrefix:", productUrlPrefix)
  
  const defaultPrefix = product.isVirtual ? '/catalog/item' : '/catalog/products'
  const prefix = productUrlPrefix || defaultPrefix
  console.log({prod_id: product.prod_id, id: product.id, isVirtual: product.isVirtual, prefix, sku: product.prod_sku });
  

  if (product.isVirtual) {
    return `${prefix}/${product.id}`
  }                        

  return `${prefix}/${product.prod_id}`
}
