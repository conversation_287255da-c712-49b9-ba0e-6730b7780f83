import type { CategoryProduct } from '@/types/GetCategoryProduct';
import { clsx, type ClassValue } from 'clsx'
import { twMerge } from 'tailwind-merge'

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export function formatInitials(fullName: string | undefined): string {
  if (!fullName) return '';
  
  return fullName
    .split(' ')
    .filter(Boolean) // Убираем пустые строки
    .map(word => word.charAt(0).toUpperCase())
    .join('')
    .slice(0, 2); // Берем только первые два инициала
}

