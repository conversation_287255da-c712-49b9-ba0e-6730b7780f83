---
import { priceFormat } from '@/lib/priceFormat'
import { trpc } from '@/trpc'
import ProductBreadcrumb from '@components/ProductBreadcrumb.astro'
import ProductSimilar from '@components/ProductSimilar.astro'
import { ImageViewer } from '@components/react/ImageViewer'
import { QtyInputWithStoreObserver } from '@components/react/QtyInputWithStoreObserver'
import { SchemaViewer } from '@components/react/SchemaViewer'
import { ProductEditButton } from '@components/react/ProductEditButton'
import { getBaseUrl, getRtiImageUrl, getImageUrl } from '@/lib/config'

import Layout from '@layouts/Layout.astro'
import { Skeleton } from '@heroui/react'
import { ChevronRightIcon } from 'lucide-react'
import { ProductRkItems } from '@components/react/ProductRkItems'
import { Image } from 'astro:assets'
import { QtyInputSkeleton } from '@components/react/QtyInputSkeleton'
import ProductSchema from '@components/ProductSchema.astro'
import { ProductSeoDescription } from '@components/react/ProductSeoDescription'

const id = Number(Astro.params['id'])
let isNotFound = false
if (!id) {
  Astro.response.status = 404
  Astro.response.statusText = 'Not Found'
  isNotFound = true
}

// Условное кеширование
const isMobile = Astro.locals.isMobile
const isAdmin = Astro.locals.isAdmin
const cacheTime = isMobile ? 1800 : 3600 // Разное время кеширования для мобильных и десктопных версий

Astro.response.headers.set('Cache-Control', `public, max-age=${cacheTime}, stale-while-revalidate=${cacheTime}`)
Astro.response.headers.set('Vary', 'Accept, Cookie, Accept-Encoding, Accept-Language, User-Agent')

// Добавляем Last-Modified header
// Astro.response.headers.set('Last-Modified', new Date().toUTCString())

// Кэширование запроса на 1 час
let data: Awaited<ReturnType<typeof trpc.products.getProductByIdMeili.query>> | null = null

if (!isNotFound) {
  try {
    data = await trpc.products.getProductByIdMeili.query(id, {
      context: {
        headers: {
          'Cache-Control': 'public, max-age=1800'
        }
      }
    })
  //  console.log("🚀 ~ data:", data)

    if (!data) {
      Astro.response.status = 404
      Astro.response.statusText = 'Not Found'
      isNotFound = true
    }
  } catch (error) {
    console.log('🚀 ~ error:', error)
    Astro.response.status = 404
    Astro.response.statusText = 'Not Found'
    isNotFound = true
  }
}

if (!isNotFound && !data?.product?.prod_id) {
  Astro.response.status = 404
  Astro.response.statusText = 'Not Found'
  isNotFound = true
}

let product: any = null
let fields: any = null
if (data?.product) {
  product = data.product
  fields = data.fields
}

// Функция для форматирования prod_sku с аналогами
const formatSkuWithAnalogs = (sku: string, analogs: string | undefined) => {
  if (!analogs || analogs.trim() === '') return sku
  try {
    return `${sku} (${analogs
      .split(',')
      .map((a: string) => a.trim())
      .join(', ')})`
  } catch (error) {
    return sku
  }
}

// SEO и сопутствующие данные (заполняем только если товар найден)
let uniqueSkuNumbers = ''
let productName = ''
let productFullName = ''
let productDescription = ''

// Получаем базовый URL
const baseUrl = getBaseUrl(Astro.url)

// Канонический URL указывает на новую страницу продукта без кросс-номеров
const canonicalHref = `${baseUrl}/catalog/products/${id}`
// Блокируем индексацию устаревшего маршрута на уровне HTTP
Astro.response.headers.set('X-Robots-Tag', 'noindex, follow')


// Формируем URL изображений
let productImages: string[] = []
let mainImage: string | undefined = undefined

// Формируем абсолютный URL текущей страницы
const currentUrl = new URL(Astro.url.pathname, Astro.url.href).toString()

// Убедимся, что URL абсолютный для микроразметки
const absoluteUrl = currentUrl.startsWith('http') ? currentUrl : `${baseUrl}${Astro.url.pathname}`

const firstAnalog = () => {
  try {
    return product.prod_analogs?.split(',')?.[0]?.trim()
  } catch (error) {
    return ''
  }
}

// FAQ данные и прочее (инициализация)
let faqData: any = null

let structuredData: any = null

// Хлебные крошки
let breadcrumbsItems: any[] = []

// Alt текст для изображений
let imageAltText = ''

// Заполнение вычислимых данных, если товар найден
if (!isNotFound && product) {
  // Получаем список всех номеров для SEO (основной SKU + аналоги)
  const allSkuNumbers = [product.prod_sku, product.prod_analogsku]
  try {
    if (product.prod_analogs) {
      allSkuNumbers.push(
        ...product.prod_analogs
          .split(',')
          .map((a: string) => a.trim())
          .filter(Boolean)
      )

      try {
        const sizeArr = product.prod_size.split('*')
        const sizeToAnalogsString = [
          sizeArr.join('x'),
          sizeArr.join('х'),
          sizeArr.join('-'),
          sizeArr.join(' '),
          sizeArr.join('*'),
          sizeArr.join(' x ')
        ].join(', ')
        allSkuNumbers.push(sizeToAnalogsString)
      } catch (error) {}
    }
  } catch (error) {}

  uniqueSkuNumbers = [...new Set(allSkuNumbers)].join(', ')

  // Подготовка SEO-данных с включением всех аналогов
  productName = `${product.prod_purpose} ${formatSkuWithAnalogs(product.prod_sku, product.prod_analogs)} / ${product.prod_analogsku} ${product.prod_size}`
  productFullName = `${product.prod_purpose} ${formatSkuWithAnalogs(product.prod_sku, product.prod_analogs)} / ${product.prod_analogsku}, Бренд: ${product.prod_manuf} Размер: ${product.prod_size}, Аналоги: ${uniqueSkuNumbers}`
  productDescription = `Купить ${productFullName} в наличии. Цена: ${priceFormat(product.prod_price)}. Все аналоги: ${uniqueSkuNumbers}. Доставка по России. Применяется для ${
    product.prod_model || ''
  } ${product.prod_uses || ''}`

  // Формируем изображения
  productImages = [
    getRtiImageUrl(product?.prod_img + '.jpg' || product?.prod_analogsku + '.jpg'),
    ...(Array.isArray(product.images) ? product.images.map((i: { path: string }) => getImageUrl(i.path)) : []),
    getRtiImageUrl(product?.prod_type + '.jpg')
  ]
  mainImage = getRtiImageUrl(product?.prod_img + '.jpg' || product?.prod_analogsku + '.jpg')

  // FAQ
  faqData = {
    '@context': 'https://schema.org',
    '@type': 'FAQPage',
    'mainEntity': [
      {
        '@type': 'Question',
        'name': `Какие аналоги у ${product.prod_sku}?`,
        'acceptedAnswer': {
          '@type': 'Answer',
          'text': `Аналоги ${product.prod_sku}: ${uniqueSkuNumbers}. Все указанные номера взаимозаменяемы.`
        }
      },
      {
        '@type': 'Question',
        'name': `Где применяется ${product.prod_purpose} ${product.prod_sku}?`,
        'acceptedAnswer': {
          '@type': 'Answer',
          'text': `${product.prod_purpose} ${product.prod_sku} применяется в ${product.prod_model || 'различных механизмах'} ${
            product.prod_uses || ''
          }. Размер: ${product.prod_size}`
        }
      },
      {
        '@type': 'Question',
        'name': `В наличии ли ${product.prod_sku}?`,
        'acceptedAnswer': {
          '@type': 'Answer',
          'text': `${product.prod_count > 0 ? 'Да, в наличии' : 'Под заказ'}: ${product.prod_count} шт. Цена: ${priceFormat(product.prod_price)}`
        }
      }
    ]
  }

  // Structured Data
  structuredData = {
    '@context': 'https://schema.org/',
    '@type': 'Product',
    name: productName,
    description: productDescription,
    sku: product.prod_analogsku,
    mpn: formatSkuWithAnalogs(product.prod_sku, product.prod_analogs),
    additionalProperty: [
      { '@type': 'PropertyValue', name: 'Основной артикул', value: product.prod_sku },
      { '@type': 'PropertyValue', name: 'Аналог артикула', value: product.prod_analogsku },
      ...(product.prod_analogs
        ? [{ '@type': 'PropertyValue', name: 'Кросс-номера', value: product.prod_analogs }]
        : []),
      { '@type': 'PropertyValue', name: 'Все номера', value: uniqueSkuNumbers },
      { '@type': 'PropertyValue', name: 'Применение', value: `${product.prod_model || ''} ${product.prod_uses || ''}`.trim() }
    ],
    brand: { '@type': 'Brand', name: product.prod_manuf },
    manufacturer: { '@type': 'Organization', name: product.prod_manuf },
    model: formatSkuWithAnalogs(product.prod_sku, product.prod_analogs),
    productID: product.prod_analogsku,
    category: product.prod_type,
    image: productImages.map((img) => ({ '@type': 'ImageObject', url: img, width: 800, height: 800 })),
    offers: {
      '@type': 'Offer',
      url: absoluteUrl,
      price: product.prod_price,
      priceCurrency: 'RUB',
      priceValidUntil: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
      itemCondition: 'https://schema.org/NewCondition',
      availability: product.prod_count > 0 ? 'https://schema.org/InStock' : 'https://schema.org/OutOfStock',
      seller: { '@type': 'Organization', name: 'Мир Сальников' }
    }
  }

  // Хлебные крошки
  breadcrumbsItems = [
    { '@type': 'ListItem', position: 1, name: 'Главная', item: baseUrl },
    { '@type': 'ListItem', position: 2, name: 'Каталог', item: `${baseUrl}/catalog` },
    { '@type': 'ListItem', position: 3, name: product.prod_type, item: `${baseUrl}/catalog/${product.prod_type}` },
    { '@type': 'ListItem', position: 4, name: productName, item: canonicalHref }
  ]

  // Alt
  imageAltText = `${product.prod_purpose} ${formatSkuWithAnalogs(product.prod_sku, product.prod_analogs)} ${product.prod_size} - ${uniqueSkuNumbers}`
}
---

{isNotFound ? (
  <Layout title='Товар не найден' description=''>
    <div class='-mt-5 flex h-96 flex-col items-center justify-center gap-5 bg-danger-100/20 p-2'>
      <div class='mt-5 sm:mt-10 text-lg font-bold text-danger-600'>Товар не найден или удалён.</div>
      <div><a href='/' class='rounded bg-default-200 px-4 py-2 text-default-600 font-semibold underline'>На главную</a></div>
    </div>
  </Layout>
) : (
  <Layout title={productFullName} description={productDescription} image={mainImage}>
  <!-- RDFa разметка для товара с аналогами -->
  <div vocab='https://schema.org/' typeof='Product' style='display:none'>
    <span property='name'>{productFullName}</span>
    <span property='description'>{productDescription}</span>
    <span property='image'>{mainImage}</span>
    <a property='url' href={absoluteUrl}></a>
    <span property='sku'>{product.prod_analogsku}</span>
    <span property='mpn'>{formatSkuWithAnalogs(product.prod_sku, product.prod_analogs)}</span>
    <span property='model'>{formatSkuWithAnalogs(product.prod_sku, product.prod_analogs)}</span>
    <span property='brand' typeof='Brand'>
      <span property='name'>{product.prod_manuf}</span>
    </span>
    {
      product.prod_analogs && (
        <span property='additionalProperty' typeof='PropertyValue'>
          <span property='name'>Кросс-номера</span>
          <span property='value'>{product.prod_analogs}</span>
        </span>
      )
    }
    <span property='additionalProperty' typeof='PropertyValue'>
      <span property='name'>Все номера</span>
      <span property='value'>{uniqueSkuNumbers}</span>
    </span>
    <span property='offers' typeof='Offer'>
      <a property='url' href={absoluteUrl}></a>
      <span property='price'>{product.prod_price}</span>
      <span property='priceCurrency'>RUB</span>
      <link property='itemCondition' href='https://schema.org/NewCondition' />
      <link property='availability' href={product.prod_count > 0 ? 'https://schema.org/InStock' : 'https://schema.org/OutOfStock'} />
    </span>
  </div>
  <!-- Основные мета-теги -->
  <link rel='canonical' href={canonicalHref} />

  <!-- Open Graph -->
  <meta property='og:type' content='product' />
  <meta property='og:url' content={canonicalHref} />
  <meta property='og:title' content={productFullName} />
  <meta property='og:description' content={productDescription} />
  <meta property='og:image' content={mainImage} />
  <meta property='og:site_name' content='Мир Сальников' />

  <!-- Для мессенджеров -->
  <meta property='og:image:width' content='800' />
  <meta property='og:image:height' content='800' />
  <meta property='og:image:alt' content={imageAltText} />

  <!-- Только нужные Twitter теги -->
  <meta name='twitter:card' content='summary_large_image' />
  <meta name='twitter:image' content={mainImage} />
  <meta name='twitter:title' content={productFullName} />
  <meta name='twitter:description' content={productDescription} />

  <!-- Дополнительные теги для WhatsApp и Telegram -->
  <meta property='og:image:secure_url' content={mainImage} />

  <!-- Расширенные мета-теги с аналогами для лучшей индексации -->
  <meta
    name='keywords'
    content={`${uniqueSkuNumbers}, ${product.prod_purpose}, ${product.prod_manuf}, ${product.prod_type}, ${product.prod_purpose}, oil seal, oilseals, РТИ, купить сальник, аналог, кросс-номер, ${product.prod_analogs}`}
  />
  <meta name='robots' content='noindex, follow' />
  <meta name='author' content='Мир Сальников' />

  <!-- Дополнительные мета-теги для поисковиков / OpenGraph product -->
  <meta property='product:retailer_item_id' content={product.prod_analogsku} />
  <meta property='product:item_group_id' content={formatSkuWithAnalogs(product.prod_sku, product.prod_analogs)} />
  <meta property='product:brand' content={product.prod_manuf} />
  <meta property='product:availability' content={product.prod_count > 0 ? 'in stock' : 'out of stock'} />
  <meta property='product:price:amount' content={product.prod_price.toString()} />
  <meta property='product:price:currency' content='RUB' />

  <!-- Яндекс-специфичные мета-теги для лучшей индексации -->
  <meta name='description' content={productDescription} />

  <!-- Preload изображений -->
  <link rel='preload' href={mainImage} as='image' fetchpriority='high' />
  {product.images?.map((img: { path: string }) => <link rel='preload' href={getImageUrl(img.path)} as='image' fetchpriority='low' />)}

  <!-- Structured Data для товара -->
  <script
    is:inline
    type='application/ld+json'
    set:html={JSON.stringify({
      ...structuredData,
      url: absoluteUrl
    })}
  />

  <!-- FAQ структурированные данные для лучшей индексации аналогов -->
  <script is:inline type='application/ld+json' set:html={JSON.stringify(faqData)} />

  <!-- Дополнительная разметка для хлебных крошек -->
  <script
    is:inline
    type='application/ld+json'
    set:html={JSON.stringify({
      '@context': 'https://schema.org',
      '@type': 'BreadcrumbList',
      'itemListElement': breadcrumbsItems
    })}
  />

  <!-- Preload critical assets -->
  <link rel='preload' href={mainImage} as='image' fetchpriority='high' />

  <div class='mx-auto px-4 py-8 sm:px-6 lg:px-8'>
    <div>
      <ProductBreadcrumb server:defer transition:persist product={product}>
        <div slot='fallback'>
          <nav aria-label='Breadcrumb' class='mx-4'>
            <ol class='flex items-center'>
              {
                [1, 2, 3].map((item, index) => (
                  <li class='flex items-center'>
                    {index > 0 && (
                      <span class='mx-2 h-4 w-6 text-sm text-default-300' aria-hidden='true'>
                        <ChevronRightIcon className='h-4 w-4' />
                      </span>
                    )}
                    <Skeleton className='h-6 w-24 rounded' />
                  </li>
                ))
              }
            </ol>
          </nav>
        </div>
      </ProductBreadcrumb>
    </div>

    <div class='mt-8 grid grid-cols-1 gap-12 md:grid-cols-2'>
      <!-- Right Column - Product Info -->
      <div class='space-y-8'>
        <div class='border-b border-default-200 pb-6'>
          <h1 class='text-base font-bold text-default-800 sm:text-xl md:text-2xl'>
            {`${product.prod_purpose} ${product.prod_sku} ${firstAnalog()} ${product.prod_manuf}, Размер: ${product.prod_size}`}
          </h1>
          <p class='mt-2 text-default-800'>Аналог: {product.prod_analogsku}</p>
          {product.prod_analogs && <h2 class='text-sm text-default-800 lg:text-base'>Кросс-номера: {product.prod_analogs}</h2>}
          {
            isAdmin && (
              <div>
                <ProductEditButton productId={id} client:visible />
              </div>
            )
          }
        </div>

        <div class='flex flex-wrap items-center justify-between'>
          <div class='space-y-2'>
            <p class='text-base font-bold text-default-800 sm:text-xl'>
              Цена: {priceFormat(product.prod_price)}
            </p>
            <p class='text-base font-semibold text-default-800 sm:text-xl'>
              Опт: {priceFormat(product.whosaleprice || 0)}
            </p>
            <div class='flex items-center gap-2'>
              <div class={`w-3 h-3 rounded-full ${product.prod_count > 0 ? 'bg-success-500' : 'bg-danger-500'}`}></div>
              <span class='text-sm text-default-600 md:text-base'>
                {product.prod_count > 0 ? 'В наличии' : 'Нет в наличии'}: {product.prod_count} шт
              </span>
            </div>
          </div>
          <div class='flex gap-2'>
            <div class='w-38 rounded-lg border p-3'>
              <QtyInputWithStoreObserver label='В корзину' product={product} client:only='react'>
                <div slot='fallback'>
                  <QtyInputSkeleton qty={product.qty} />
                </div>
              </QtyInputWithStoreObserver>
            </div>
          </div>
        </div>

        <div class='space-y-4 rounded-xl bg-default-50 p-6'>
          {
            fields
              .filter((field: { keyname: string; title?: string; type?: 'html' | 'array'; searchable?: boolean }) => {
                // Проверяем наличие значения в поле
                const value = product[field.keyname]
                return value !== undefined && value !== null && value !== ''
              })
              .map((field: { keyname: string; title?: string; type?: 'html' | 'array'; searchable?: boolean }) => (
                <div class='flex flex-col justify-between gap-2 border-b border-default-200 py-2 last:border-none sm:flex-row sm:items-center'>
                  <div class='text-base font-medium text-default-700'>{field.title}</div>
                  <div class='max-w-full text-right sm:max-w-80'>
                    {'type' in field && field.type === 'html' ? (
                      <div set:html={product[field.keyname]} class='text-right text-base text-default-900' />
                    ) : 'type' in field && field.type === 'array' ? (
                      <div class='flex flex-wrap justify-end gap-2'>
                        {product[field.keyname]
                          ?.split(',')
                          .map((i: string) => i.trim())
                          .filter(Boolean)
                          .map((item: any) => (
                            <span data-astro-prefetch='tap' class='rounded-full bg-default-100 px-2 py-1 text-sm transition-colors hover:bg-default-200'>
                              {item}
                            </span>
                          ))}
                      </div>
                    ) : (
                      <span class={`text-base ${'searchable' in field && field.searchable ? 'hover:underline' : 'text-default-900'}`}>
                        {'searchable' in field && field.searchable ? (
                          <a data-astro-prefetch='tap' href={`/search/${encodeURIComponent(product[field.keyname])}`}>
                            {product[field.keyname]}
                          </a>
                        ) : (
                          product[field.keyname]
                        )}
                      </span>
                    )}
                  </div>
                </div>
              ))
          }
        </div>

        <!-- FAQ секция для лучшей индексации аналогов -->
        {
          product.prod_analogs && (
            <div class='rounded-xl bg-default-50 p-6'>
              <h3 class='mb-4 text-lg font-semibold text-default-800'>Часто задаваемые вопросы</h3>
              <div class='space-y-4'>
                <details class='border-b border-default-200 pb-4 last:border-none'>
                  <summary class='cursor-pointer text-base font-medium text-default-700 hover:text-default-900'>Какие аналоги у {' '} {product.prod_sku}?</summary>
                  <p class='mt-2 text-sm text-default-600'>
                    Аналоги {product.prod_sku}: <strong>{uniqueSkuNumbers}</strong>. Все указанные номера взаимозаменяемы.
                  </p>
                </details>
                <details class='border-b border-default-200 pb-4 last:border-none'>
                  <summary class='cursor-pointer text-base font-medium text-default-700 hover:text-default-900'>
                    Где применяется {product.prod_purpose} {' '} {product.prod_sku}?
                  </summary>
                  <p class='mt-2 text-sm text-default-600'>
                    {product.prod_purpose} {product.prod_sku} применяется в {product.prod_model || 'различных механизмах'} {product.prod_uses || ''}. Размер: {' '}
                    {product.prod_size}
                  </p>
                </details>
                <details class='border-b border-default-200 pb-4 last:border-none'>
                  <summary class='cursor-pointer text-base font-medium text-default-700 hover:text-default-900'>
                    В наличии ли {product.prod_purpose} {' '} {product.prod_sku}?
                  </summary>
                  <p class='mt-2 text-sm text-default-600'>
                    {product.prod_count > 0 ? 'Да, в наличии' : 'Под заказ'}: {product.prod_count} шт. Цена: {priceFormat(product.prod_price)}
                  </p>
                </details>
              </div>
            </div>
          )
        }

        {
          product.prod_rk && product.prod_cat == '5' && (
            <div>
              <h3 class='mb-3 font-semibold text-default-800 md:text-lg'>Состав ремкомлекта </h3>
              <ProductRkItems rkString={product.prod_rk} skeletonCount={product.prod_rk?.split?.(',')?.length} id={product.prod_id} client:only='react' />
            </div>
          )
        }
      </div>

      <!-- Left Column - Images -->
      <div class='sticky top-8'>
        <div class='flex flex-col gap-5'>
          <div class='rounded-lg border border-default-300 p-3'>
            <ImageViewer imageHeight={450} client:visible product={product}>
              <div class='rounded-lg border p-3' slot='fallback'>
                <Image src={getRtiImageUrl(`${product?.prod_img || product?.prod_analogsku}.jpg`)} width={600} height={450} alt={imageAltText} />
              </div>
            </ImageViewer>
          </div>
          <!-- {
            schema && (
              <div class='flex flex-col gap-2 rounded-lg border p-3'>
                <div>
                  <SchemaViewer isMobile={isMobile} server:defer client:only='react' data={schema} product={product} />
                </div>
              </div>
            )
          } -->

          <div class='flex flex-col gap-2 rounded-lg border p-3'>
            <div>
              <ProductSchema server:defer product={product} />
            </div>
          </div>
        </div>
      </div>
    </div>
    <ProductSeoDescription product={product} client:only='react' />
  </div>
  </Layout>
)}
