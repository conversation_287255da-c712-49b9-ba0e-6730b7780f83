import { atom } from 'nanostores'

/**
 * Стор для хранения статуса администратора
 * Совместим с SSR - значение устанавливается в middleware
 */
export const isAdmin = atom<boolean>(false)

/**
 * Устанавливает статус администратора
 * @param value - статус администратора
 */
export const setIsAdmin = (value: boolean) => {
  isAdmin.set(value)
}

/**
 * Получает текущий статус администратора
 * @returns текущий статус администратора
 */
export const getIsAdmin = () => isAdmin.get()
