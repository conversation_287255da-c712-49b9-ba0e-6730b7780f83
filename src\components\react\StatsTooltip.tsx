import { useState, useEffect } from 'react'
import { <PERSON><PERSON><PERSON>, Spinner } from "@heroui/react"
import type { CategoryProduct } from '@/types/GetCategoryProduct'

interface Props {
  product: CategoryProduct
  children: React.ReactNode
  editorModeEnable: boolean
}

export const StatsTooltip = ({ product, children, editorModeEnable }: Props) => {
  const [loading, setLoading] = useState(false)
  const [stats, setStats] = useState<{ qStat: string | number, sStat: string | number } | null>(null)
  const [isOpen, setIsOpen] = useState(false)

  // Функция для загрузки статистики
  const loadStats = async () => {
    // console.log("🚀 ~ loadStats ~ product:", product)
    if (!product?.prod_analogsku || !editorModeEnable) {
      return
    }

    // Если статистика уже загружена, не делаем повторный запрос
    if (stats && (stats.qStat !== '--' || stats.sStat !== '--')) {
      return
    }

    setLoading(true)

    try {
      // Добавляем случайный параметр для предотвращения кэширования
      const cacheBuster = `_t=${Date.now()}`
      const res = await fetch(`/api/cpan/statistics/byoem/${encodeURIComponent(product.prod_original_analogsku || product.prod_analogsku)}?sales=true&stat=true&${cacheBuster}`)

      if (res.ok) {
        const data = await res.json()
        
        setStats({
          qStat: data?.stat || '--',
          sStat: data?.sales?.sales || '--'
        })
      } else {
        setStats({ qStat: '--', sStat: '--' })
      }
    } catch (error) {
      console.error('Ошибка при загрузке статистики:', error)
      setStats({ qStat: '--', sStat: '--' })
    } finally {
      setLoading(false)
    }
  }

  // Загружаем статистику при открытии тултипа
  useEffect(() => {
    if (isOpen && editorModeEnable) {
      loadStats()
    }
  }, [isOpen, product.prod_id])

  // Если режим редактирования отключен, просто возвращаем дочерний элемент
  if (!editorModeEnable) {
    return <>{children}</>
  }

  return (
    <Tooltip
      size='sm'
      shadow='lg'
      closeDelay={0}
      showArrow
      motionProps={{
        variants: {
          exit: {
            opacity: 0,
            transition: {
              duration: 0.05,
              ease: 'backIn'
            }
          },
          enter: {
            opacity: 1,
            transition: {
              duration: 0.05,
              ease: 'backOut'
            }
          }
        }
      }}
      disableAnimation
      classNames={{ base: 'pointer-events-none', content: 'p-2 rounded-lg shadow-xl' }}
      content={
        <div className='px-1 py-2'>
          <div className='text-sm'>
            {loading ? (
              <Spinner size='sm' color='warning' />
            ) : (
              <div>
                Спрос: <b>{stats?.qStat || '--'}</b> | Продажи: <b>{stats?.sStat || '--'}</b>
              </div>
            )}
          </div>
        </div>
      }
      onOpenChange={(open) => setIsOpen(open)}
    >
      {children}
    </Tooltip>
  )
}
