import { memo, useState, useEffect, useCallback } from 'react'
import { Card, CardBody, Divider, Button, Image } from '@heroui/react'
import { priceFormat } from '@/lib/priceFormat'
import { QtyInput } from './QtyInput'
import { getRtiImageUrl, getImageUrl } from '@/lib/config'
import { ImageViewer } from './ImageViewer'
import { trpc } from '@/trpc'
import { ExternalLinkIcon } from 'lucide-react'
import { getProductLink } from '@/lib/utils'

interface ProductQuickViewProps {
  product: any
  onClose?: () => void
  fields?: any[]
  isLoading?: boolean
}

export const ProductQuickView = memo(({ product, onClose, isLoading = false }: ProductQuickViewProps) => {
  // TODO: загружать все данные из компонента
  const [isMobile, setIsMobile] = useState(true)

  useEffect(() => {
    const checkMobile = () => setIsMobile(window.innerWidth < 768)
    checkMobile()
    window.addEventListener('resize', checkMobile)
    return () => window.removeEventListener('resize', checkMobile)
  }, [])

  // Подготовка полей для отображения
  const fields = [
    {
      keyname: 'prod_sku',
      title: 'Артикул'
    },
    {
      keyname: 'prod_analogsku',
      title: 'Аналог'
    },
    {
      keyname: 'prod_size',
      title: 'Размер',
      searchable: true
    },
    {
      keyname: 'prod_manuf',
      title: 'Производитель',
      searchable: true
    },
    {
      keyname: 'prod_year',
      title: 'Год'
    },
    {
      keyname: 'prod_type',
      title: 'Тип',
      searchable: true
    },
    {
      keyname: 'prod_uses',
      title: 'Применение',
      searchable: true
    },
    {
      keyname: 'prod_analogs',
      title: 'Аналоги',
      type: 'array'
    },
    {
      keyname: 'prod_model',
      title: 'Модель',
      searchable: true
    },
    {
      keyname: 'prod_note',
      title: 'Описание',
      type: 'html'
    },
    { title: 'Наличие', keyname: 'prod_count', render: (value) => `${value} шт.` }
  ]

  return (
    <div className='grid grid-cols-1 gap-6 md:grid-cols-2'>
      {/* Левая колонка - Изображение */}
      <div className='flex flex-col gap-4'>
        <div className='rounded-lg border border-default-200 p-3'>
          <ImageViewer imageHeight={isMobile ? 200 : 300} product={product} isMobile={isMobile} />
        </div>
      </div>

      {/* Правая колонка - Информация о товаре */}
      <div className='flex flex-col gap-4'>
        <div className='border-b border-default-200 pb-4'>
          <h2 className='text-xl font-bold text-default-900'>
            {product.prod_purpose} {product.prod_sku}
          </h2>
          <p className='mt-2 text-lg text-default-600'>
            {product.prod_manuf} {product.prod_size}
          </p>
        </div>

        {!isLoading && (
          <div>
            <div className='flex items-center justify-between'>
              <div className='space-y-2'>
                <p className='text-xl font-bold text-default-600'>{priceFormat(product.prod_price)}</p>
                <div className='flex items-center gap-2'>
                  <div className={`h-3 w-3 rounded-full ${product.prod_count > 0 ? 'bg-success-500' : 'bg-danger-500'}`}></div>
                  <span className='text-sm text-default-600'>{product.prod_count > 0 ? 'В наличии' : 'Нет в наличии'}</span>
                </div>
              </div>
              <div className='w-38 rounded-lg border p-3'>
                <QtyInput label='В корзину' product={product} />
              </div>
            </div>

            <div className='mt-auto flex justify-center'>
              <Button variant='light' as='a' href={getProductLink({product })} className=''>
                Перейти на страницу товара <ExternalLinkIcon />
              </Button>
            </div>
          </div>
        )}

        <div className='space-y-3 rounded-xl bg-default-50 p-4'>
          {isLoading ? (
            <div className='flex items-center justify-center p-4'>
              <div className='h-6 w-6 animate-spin rounded-full border-2 border-primary border-t-transparent'></div>
              <span className='ml-2'>Загрузка...</span>
            </div>
          ) : (
            fields.map((field) => (
              <div key={field.keyname} className='flex items-center justify-between border-b border-default-200 py-2 last:border-none'>
                <span className='text-sm font-medium text-default-700'>{field.title}</span>
                {field.type !== 'html' ? (
                  field.type === 'array' ? (
                    <div className='flex flex-wrap justify-end gap-2'>
                      {product[field.keyname]
                        ?.split(',')
                        .filter((i: string) => i)
                        .map((item: string, index: number) => (
                          <span key={index} className='rounded-full bg-default-100 px-3 py-1 text-xs'>
                            {item}
                          </span>
                        ))}
                    </div>
                  ) : (
                    <span className='text-sm text-default-900'>{product[field.keyname]}</span>
                  )
                ) : (
                  <div dangerouslySetInnerHTML={{ __html: product[field.keyname] }} className='text-sm text-default-900' />
                )}
              </div>
            ))
          )}
        </div>
      </div>
    </div>
  )
})

ProductQuickView.displayName = 'ProductQuickView'
