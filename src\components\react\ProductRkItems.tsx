import { trpcReact } from '@/trpc'
import { TrpcReactWrapper } from './TrcpReactWrapper'
import { <PERSON><PERSON>, <PERSON>, <PERSON>F<PERSON>er, CardHeader, Image, Skeleton } from '@heroui/react'
import { ExternalLinkIcon } from 'lucide-react'
import { useMemo } from 'react'
import { getRtiImageUrl } from '@/lib/config'
import { getProductLink } from '@/lib/utils'

interface Props {
  id: number
  skeletonCount?: number
  rkString?: string
}

type RkItem = {
  id: string
  qty: number
}

const parseRkString = (rkString: string = ''): Record<string, number> => {
  return rkString.split(',').reduce(
    (acc, item) => {
      const [id, qty] = item.split(/[*:]/)
      if (id) {
        acc[id] = Number(qty) || 1
      }
      return acc
    },
    {} as Record<string, number>
  )
}

export const ProductRkItems = ({ skeletonCount = 3, rkString = '', ...props }: Props) => {
  return (
    <TrpcReactWrapper>
      <RkItems skeletonCount={skeletonCount} rkString={rkString} {...props} />
    </TrpcReactWrapper>
  )
}

const RkItems = ({ id, skeletonCount, rkString }: Props) => {
  const { data, isFetching, isSuccess } = trpcReact.products.getRkProducts.useQuery(id)
  const rkItems = useMemo(() => parseRkString(rkString), [rkString])

  return (
    <div>
      {isFetching && (
        <div className='flex flex-wrap items-center justify-center gap-4'>
          {[...Array(skeletonCount)].map((_, i) => (
            <Card key={`skeleton-${i}-${Date.now()}`} className='h-[200px] min-w-64 max-w-xs'>
              <CardHeader className='absolute top-1 z-10 flex-col items-start gap-2'>
                <Skeleton className='h-6 w-48 rounded-lg' />
                <Skeleton className='h-4 w-32 rounded-lg' />
              </CardHeader>
              <Skeleton className='h-full w-full rounded-lg' />
              <CardFooter className='absolute bottom-0 z-10 justify-between'>
                <Skeleton className='h-8 w-24 rounded-lg' />
                <Skeleton className='h-8 w-24 rounded-lg' />
              </CardFooter>
            </Card>
          ))}
        </div>
      )}
      {isSuccess && (
        <div className='flex flex-wrap items-center justify-center gap-4'>
          {data.map((p) => (
            <Card
              key={p.prod_id}
              as='div'
              isPressable
              isBlurred
              isFooterBlurred
              className='h-[200px] min-w-64 max-w-xs cursor-pointer'
              data-product-id={p.prod_id}
            >
              <CardHeader className='absolute top-1 z-10 flex-col items-start gap-2 bg-white/95 dark:bg-default/95'>
                <span className='font-medium text-default-800'>
                  {p.prod_purpose} {p.prod_sku}
                </span>
                <p className='text-sm text-default-700'>
                  {p.prod_type} {p.prod_size}
                </p>
                {rkItems[p.prod_id] && (
                  <p className='text-xs md:text-sm'>
                    Количество в РК: <b>{rkItems[p.prod_id]}</b> шт.
                  </p>
                )}
              </CardHeader>
              <Image
                removeWrapper
                alt={p.prod_sku}
                className='z-0 h-full w-full -translate-y-6 scale-95 rounded-lg object-cover'
                src={getRtiImageUrl(`${p.prod_img || p.prod_analogsku}.jpg`)}
              />
              <CardFooter className='absolute bottom-0 z-10 justify-between border-t-1 border-default-100/50 bg-white/80'>
                <div>
                  <p className='text-sm text-default-800'>{p.prod_analogsku}</p>
                </div>
                <div className='flex gap-2'>
                  <Button as='a' href={getProductLink({ product: p })} className='text-xs' color='default' size='sm' target='_blank'>
                    Перейти <ExternalLinkIcon className='w-4' />
                  </Button>
                </div>
              </CardFooter>
            </Card>
          ))}
        </div>
      )}
    </div>
  )
}
